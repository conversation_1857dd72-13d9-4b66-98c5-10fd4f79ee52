/**
 * Performance Optimization Engine
 * Advanced performance monitoring and optimization
 */

class PerformanceOptimizer {
    constructor() {
        this.metrics = {
            fps: 0,
            memory: 0,
            loadTime: 0,
            renderTime: 0,
            networkRequests: 0
        };
        this.observers = new Map();
        this.lazyImages = new Set();
        this.preloadedResources = new Set();
        this.criticalResources = new Set();
        this.performanceEntries = [];
        this.isMonitoring = false;
    }

    init() {
        this.setupPerformanceMonitoring();
        this.setupLazyLoading();
        this.setupResourcePreloading();
        this.setupCriticalResourceLoading();
        this.setupImageOptimization();
        this.setupMemoryManagement();
        this.setupNetworkOptimization();
        this.startPerformanceTracking();
        console.log('⚡ Performance Optimizer initialized');
    }

    setupPerformanceMonitoring() {
        // FPS Monitoring
        let lastTime = performance.now();
        let frameCount = 0;
        
        const measureFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime >= lastTime + 1000) {
                this.metrics.fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                // Trigger optimization if FPS drops
                if (this.metrics.fps < 30) {
                    this.optimizeForLowFPS();
                }
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);

        // Memory Monitoring
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.memory = performance.memory.usedJSHeapSize / 1048576; // MB
                
                // Trigger garbage collection hint if memory usage is high
                if (this.metrics.memory > 100) {
                    this.optimizeMemoryUsage();
                }
            }, 5000);
        }

        // Network Performance
        if ('connection' in navigator) {
            const connection = navigator.connection;
            this.adaptToNetworkConditions(connection);
            
            connection.addEventListener('change', () => {
                this.adaptToNetworkConditions(connection);
            });
        }
    }

    setupLazyLoading() {
        // Advanced Intersection Observer for lazy loading
        const lazyImageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.loadImageOptimized(img);
                    lazyImageObserver.unobserve(img);
                    this.lazyImages.delete(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all images with data-src
        document.querySelectorAll('img[data-src]').forEach(img => {
            lazyImageObserver.observe(img);
            this.lazyImages.add(img);
        });

        this.observers.set('lazyImages', lazyImageObserver);

        // Lazy load other content
        const contentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    this.loadContentLazily(element);
                    contentObserver.unobserve(element);
                }
            });
        }, {
            rootMargin: '100px 0px',
            threshold: 0.1
        });

        document.querySelectorAll('[data-lazy-content]').forEach(el => {
            contentObserver.observe(el);
        });

        this.observers.set('lazyContent', contentObserver);
    }

    loadImageOptimized(img) {
        const src = img.dataset.src;
        const webpSrc = img.dataset.webp;
        const avifSrc = img.dataset.avif;
        
        // Create a new image to test loading
        const newImg = new Image();
        
        // Progressive enhancement: try modern formats first
        const sources = [avifSrc, webpSrc, src].filter(Boolean);
        
        this.loadImageWithFallback(newImg, sources, 0)
            .then(() => {
                img.src = newImg.src;
                img.classList.add('loaded');
                
                // Add fade-in animation
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s ease';
                requestAnimationFrame(() => {
                    img.style.opacity = '1';
                });
            })
            .catch(() => {
                img.classList.add('error');
                console.warn('Failed to load image:', src);
            });
    }

    loadImageWithFallback(img, sources, index) {
        return new Promise((resolve, reject) => {
            if (index >= sources.length) {
                reject(new Error('All image sources failed'));
                return;
            }
            
            img.onload = () => resolve();
            img.onerror = () => {
                this.loadImageWithFallback(img, sources, index + 1)
                    .then(resolve)
                    .catch(reject);
            };
            
            img.src = sources[index];
        });
    }

    loadContentLazily(element) {
        const contentType = element.dataset.lazyContent;
        
        switch (contentType) {
            case 'iframe':
                this.loadIframeLazily(element);
                break;
            case 'video':
                this.loadVideoLazily(element);
                break;
            case 'component':
                this.loadComponentLazily(element);
                break;
            default:
                this.loadGenericContent(element);
        }
    }

    loadIframeLazily(element) {
        const src = element.dataset.src;
        if (src) {
            element.src = src;
            element.removeAttribute('data-src');
        }
    }

    loadVideoLazily(element) {
        const sources = element.querySelectorAll('source[data-src]');
        sources.forEach(source => {
            source.src = source.dataset.src;
            source.removeAttribute('data-src');
        });
        element.load();
    }

    setupResourcePreloading() {
        // Intelligent resource preloading
        const preloadCriticalResources = () => {
            // Preload fonts
            this.preloadFonts([
                'Inter',
                'Playfair Display',
                'Tajawal'
            ]);
            
            // Preload critical images
            this.preloadImages([
                '/images/hero-bg.webp',
                '/images/logo.svg',
                '/images/featured-book.webp'
            ]);
            
            // Preload critical CSS
            this.preloadCSS([
                '/css/critical.css',
                '/css/components.css'
            ]);
        };
        
        // Preload on idle
        if ('requestIdleCallback' in window) {
            requestIdleCallback(preloadCriticalResources);
        } else {
            setTimeout(preloadCriticalResources, 100);
        }
    }

    preloadFonts(fontFamilies) {
        fontFamilies.forEach(family => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'font';
            link.type = 'font/woff2';
            link.crossOrigin = 'anonymous';
            link.href = `https://fonts.googleapis.com/css2?family=${family.replace(' ', '+')}:wght@300;400;500;600;700&display=swap`;
            document.head.appendChild(link);
        });
    }

    preloadImages(imagePaths) {
        imagePaths.forEach(path => {
            if (!this.preloadedResources.has(path)) {
                const img = new Image();
                img.src = path;
                this.preloadedResources.add(path);
            }
        });
    }

    preloadCSS(cssPaths) {
        cssPaths.forEach(path => {
            if (!this.preloadedResources.has(path)) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'style';
                link.href = path;
                document.head.appendChild(link);
                this.preloadedResources.add(path);
            }
        });
    }

    setupCriticalResourceLoading() {
        // Load critical resources first
        const criticalCSS = `
            /* Critical CSS - Above the fold styles */
            body { font-family: system-ui, sans-serif; }
            .hero { min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
        `;
        
        const style = document.createElement('style');
        style.textContent = criticalCSS;
        document.head.appendChild(style);
    }

    setupImageOptimization() {
        // Responsive image loading
        const updateImageSources = () => {
            const devicePixelRatio = window.devicePixelRatio || 1;
            const viewportWidth = window.innerWidth;
            
            document.querySelectorAll('img[data-responsive]').forEach(img => {
                const baseUrl = img.dataset.responsive;
                let width = Math.min(viewportWidth, 1920);
                
                // Adjust for high DPI displays
                if (devicePixelRatio > 1) {
                    width *= Math.min(devicePixelRatio, 2);
                }
                
                const optimizedUrl = `${baseUrl}?w=${width}&q=80&f=webp`;
                
                if (img.src !== optimizedUrl) {
                    img.src = optimizedUrl;
                }
            });
        };
        
        // Update on resize with debouncing
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(updateImageSources, 250);
        });
        
        updateImageSources();
    }

    setupMemoryManagement() {
        // Automatic cleanup of unused resources
        setInterval(() => {
            this.cleanupUnusedResources();
        }, 30000); // Every 30 seconds
        
        // Page visibility API for resource management
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseNonEssentialOperations();
            } else {
                this.resumeOperations();
            }
        });
    }

    cleanupUnusedResources() {
        // Remove images that are far from viewport
        this.lazyImages.forEach(img => {
            const rect = img.getBoundingClientRect();
            const isVeryFarFromViewport = rect.top > window.innerHeight * 3 || rect.bottom < -window.innerHeight * 3;
            
            if (isVeryFarFromViewport && img.src) {
                img.removeAttribute('src');
                img.classList.remove('loaded');
            }
        });
        
        // Suggest garbage collection
        if ('gc' in window && typeof window.gc === 'function') {
            window.gc();
        }
    }

    setupNetworkOptimization() {
        // Service Worker for caching
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
        
        // Resource hints
        this.addResourceHints();
    }

    addResourceHints() {
        // DNS prefetch for external domains
        const externalDomains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'cdnjs.cloudflare.com'
        ];
        
        externalDomains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = `//${domain}`;
            document.head.appendChild(link);
        });
        
        // Preconnect to critical domains
        const criticalDomains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com'
        ];
        
        criticalDomains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'preconnect';
            link.href = `https://${domain}`;
            link.crossOrigin = 'anonymous';
            document.head.appendChild(link);
        });
    }

    adaptToNetworkConditions(connection) {
        const effectiveType = connection.effectiveType;
        const saveData = connection.saveData;
        
        if (saveData || effectiveType === 'slow-2g' || effectiveType === '2g') {
            this.enableDataSaverMode();
        } else if (effectiveType === '4g') {
            this.enableHighQualityMode();
        }
    }

    enableDataSaverMode() {
        // Reduce image quality
        document.querySelectorAll('img[data-src]').forEach(img => {
            const src = img.dataset.src;
            if (src.includes('?')) {
                img.dataset.src = src.replace(/q=\d+/, 'q=50');
            } else {
                img.dataset.src = `${src}?q=50`;
            }
        });
        
        // Disable non-essential animations
        document.body.classList.add('reduce-motion');
        
        console.log('📱 Data saver mode enabled');
    }

    enableHighQualityMode() {
        // Enable high-quality images
        document.querySelectorAll('img[data-src]').forEach(img => {
            const src = img.dataset.src;
            if (src.includes('?')) {
                img.dataset.src = src.replace(/q=\d+/, 'q=90');
            } else {
                img.dataset.src = `${src}?q=90`;
            }
        });
        
        console.log('🚀 High quality mode enabled');
    }

    optimizeForLowFPS() {
        // Reduce animation complexity
        document.body.classList.add('low-performance');
        
        // Pause non-essential animations
        if (window.WebGLEffects) {
            window.WebGLEffects.pause();
        }
        
        console.log('⚠️ Low FPS detected, optimizing performance');
    }

    optimizeMemoryUsage() {
        // Clear unused caches
        this.cleanupUnusedResources();
        
        // Reduce particle count
        if (window.WebGLEffects && window.WebGLEffects.particles) {
            window.WebGLEffects.particles = window.WebGLEffects.particles.slice(0, 50);
        }
        
        console.log('🧹 Memory optimization triggered');
    }

    pauseNonEssentialOperations() {
        // Pause animations
        if (window.WebGLEffects) {
            window.WebGLEffects.pause();
        }
        
        // Pause video playback
        document.querySelectorAll('video').forEach(video => {
            if (!video.paused) {
                video.pause();
                video.dataset.wasPlaying = 'true';
            }
        });
    }

    resumeOperations() {
        // Resume animations
        if (window.WebGLEffects) {
            window.WebGLEffects.resume();
        }
        
        // Resume video playback
        document.querySelectorAll('video[data-was-playing]').forEach(video => {
            video.play();
            video.removeAttribute('data-was-playing');
        });
    }

    startPerformanceTracking() {
        this.isMonitoring = true;
        
        // Track Core Web Vitals
        this.trackCoreWebVitals();
        
        // Track custom metrics
        this.trackCustomMetrics();
        
        // Performance reporting
        setInterval(() => {
            this.reportPerformanceMetrics();
        }, 10000); // Every 10 seconds
    }

    trackCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            this.metrics.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                this.metrics.fid = entry.processingStart - entry.startTime;
            });
        }).observe({ entryTypes: ['first-input'] });
        
        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            this.metrics.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
    }

    trackCustomMetrics() {
        // Time to Interactive
        this.metrics.tti = performance.now();
        
        // Resource loading times
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach(entry => {
                this.performanceEntries.push({
                    name: entry.name,
                    duration: entry.duration,
                    type: entry.entryType,
                    timestamp: entry.startTime
                });
            });
        });
        
        observer.observe({ entryTypes: ['resource', 'navigation'] });
    }

    reportPerformanceMetrics() {
        if (!this.isMonitoring) return;
        
        const report = {
            timestamp: Date.now(),
            metrics: { ...this.metrics },
            userAgent: navigator.userAgent,
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            } : null
        };
        
        // Send to analytics (replace with your analytics endpoint)
        console.log('📊 Performance Report:', report);
        
        // You can send this to your analytics service
        // fetch('/api/analytics/performance', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(report)
        // });
    }

    getPerformanceScore() {
        const scores = {
            lcp: this.metrics.lcp < 2500 ? 100 : Math.max(0, 100 - (this.metrics.lcp - 2500) / 25),
            fid: this.metrics.fid < 100 ? 100 : Math.max(0, 100 - (this.metrics.fid - 100) / 2),
            cls: this.metrics.cls < 0.1 ? 100 : Math.max(0, 100 - (this.metrics.cls - 0.1) * 1000),
            fps: this.metrics.fps > 55 ? 100 : Math.max(0, this.metrics.fps * 1.8)
        };
        
        return Math.round(Object.values(scores).reduce((a, b) => a + b, 0) / Object.keys(scores).length);
    }

    destroy() {
        this.isMonitoring = false;
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.lazyImages.clear();
        this.preloadedResources.clear();
        this.performanceEntries = [];
    }
}

// Initialize Performance Optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => performanceOptimizer.init());
} else {
    performanceOptimizer.init();
}

// Export for global access
window.PerformanceOptimizer = performanceOptimizer;
