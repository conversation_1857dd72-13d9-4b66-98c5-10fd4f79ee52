/* ===================================
   PREMIUM GRID SYSTEM
   Advanced CSS Grid & Flexbox Layout
   =================================== */

/* Container System */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.container-xs { max-width: var(--container-xs); }
.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

.container-fluid {
  width: 100%;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

/* CSS Grid System */
.grid {
  display: grid;
  gap: var(--space-6);
}

/* Grid Template Columns */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
.grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
.grid-cols-9 { grid-template-columns: repeat(9, minmax(0, 1fr)); }
.grid-cols-10 { grid-template-columns: repeat(10, minmax(0, 1fr)); }
.grid-cols-11 { grid-template-columns: repeat(11, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Auto-fit and Auto-fill */
.grid-cols-auto-fit { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-cols-auto-fill { grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); }

/* Grid Template Rows */
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }

/* Grid Column Span */
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-7 { grid-column: span 7 / span 7; }
.col-span-8 { grid-column: span 8 / span 8; }
.col-span-9 { grid-column: span 9 / span 9; }
.col-span-10 { grid-column: span 10 / span 10; }
.col-span-11 { grid-column: span 11 / span 11; }
.col-span-12 { grid-column: span 12 / span 12; }
.col-span-full { grid-column: 1 / -1; }

/* Grid Row Span */
.row-span-1 { grid-row: span 1 / span 1; }
.row-span-2 { grid-row: span 2 / span 2; }
.row-span-3 { grid-row: span 3 / span 3; }
.row-span-4 { grid-row: span 4 / span 4; }
.row-span-5 { grid-row: span 5 / span 5; }
.row-span-6 { grid-row: span 6 / span 6; }
.row-span-full { grid-row: 1 / -1; }

/* Grid Column Start/End */
.col-start-1 { grid-column-start: 1; }
.col-start-2 { grid-column-start: 2; }
.col-start-3 { grid-column-start: 3; }
.col-start-4 { grid-column-start: 4; }
.col-start-5 { grid-column-start: 5; }
.col-start-6 { grid-column-start: 6; }
.col-start-7 { grid-column-start: 7; }
.col-start-8 { grid-column-start: 8; }
.col-start-9 { grid-column-start: 9; }
.col-start-10 { grid-column-start: 10; }
.col-start-11 { grid-column-start: 11; }
.col-start-12 { grid-column-start: 12; }
.col-start-13 { grid-column-start: 13; }
.col-start-auto { grid-column-start: auto; }

.col-end-1 { grid-column-end: 1; }
.col-end-2 { grid-column-end: 2; }
.col-end-3 { grid-column-end: 3; }
.col-end-4 { grid-column-end: 4; }
.col-end-5 { grid-column-end: 5; }
.col-end-6 { grid-column-end: 6; }
.col-end-7 { grid-column-end: 7; }
.col-end-8 { grid-column-end: 8; }
.col-end-9 { grid-column-end: 9; }
.col-end-10 { grid-column-end: 10; }
.col-end-11 { grid-column-end: 11; }
.col-end-12 { grid-column-end: 12; }
.col-end-13 { grid-column-end: 13; }
.col-end-auto { grid-column-end: auto; }

/* Flexbox System */
.flex { display: flex; }
.inline-flex { display: inline-flex; }

/* Flex Direction */
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

/* Flex Wrap */
.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

/* Flex */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Flex Grow */
.grow { flex-grow: 1; }
.grow-0 { flex-grow: 0; }

/* Flex Shrink */
.shrink { flex-shrink: 1; }
.shrink-0 { flex-shrink: 0; }

/* Justify Content */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Align Items */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* Align Content */
.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }
.content-evenly { align-content: space-evenly; }

/* Align Self */
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

/* Place Items */
.place-items-start { place-items: start; }
.place-items-end { place-items: end; }
.place-items-center { place-items: center; }
.place-items-stretch { place-items: stretch; }

/* Place Content */
.place-content-start { place-content: start; }
.place-content-end { place-content: end; }
.place-content-center { place-content: center; }
.place-content-between { place-content: space-between; }
.place-content-around { place-content: space-around; }
.place-content-evenly { place-content: space-evenly; }
.place-content-stretch { place-content: stretch; }

/* Place Self */
.place-self-auto { place-self: auto; }
.place-self-start { place-self: start; }
.place-self-end { place-self: end; }
.place-self-center { place-self: center; }
.place-self-stretch { place-self: stretch; }

/* Gap */
.gap-0 { gap: 0; }
.gap-px { gap: var(--space-px); }
.gap-0-5 { gap: var(--space-0-5); }
.gap-1 { gap: var(--space-1); }
.gap-1-5 { gap: var(--space-1-5); }
.gap-2 { gap: var(--space-2); }
.gap-2-5 { gap: var(--space-2-5); }
.gap-3 { gap: var(--space-3); }
.gap-3-5 { gap: var(--space-3-5); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-7 { gap: var(--space-7); }
.gap-8 { gap: var(--space-8); }
.gap-9 { gap: var(--space-9); }
.gap-10 { gap: var(--space-10); }
.gap-11 { gap: var(--space-11); }
.gap-12 { gap: var(--space-12); }
.gap-14 { gap: var(--space-14); }
.gap-16 { gap: var(--space-16); }
.gap-20 { gap: var(--space-20); }
.gap-24 { gap: var(--space-24); }
.gap-28 { gap: var(--space-28); }
.gap-32 { gap: var(--space-32); }

/* Gap X */
.gap-x-0 { column-gap: 0; }
.gap-x-px { column-gap: var(--space-px); }
.gap-x-0-5 { column-gap: var(--space-0-5); }
.gap-x-1 { column-gap: var(--space-1); }
.gap-x-1-5 { column-gap: var(--space-1-5); }
.gap-x-2 { column-gap: var(--space-2); }
.gap-x-2-5 { column-gap: var(--space-2-5); }
.gap-x-3 { column-gap: var(--space-3); }
.gap-x-3-5 { column-gap: var(--space-3-5); }
.gap-x-4 { column-gap: var(--space-4); }
.gap-x-5 { column-gap: var(--space-5); }
.gap-x-6 { column-gap: var(--space-6); }
.gap-x-7 { column-gap: var(--space-7); }
.gap-x-8 { column-gap: var(--space-8); }
.gap-x-9 { column-gap: var(--space-9); }
.gap-x-10 { column-gap: var(--space-10); }
.gap-x-11 { column-gap: var(--space-11); }
.gap-x-12 { column-gap: var(--space-12); }
.gap-x-14 { column-gap: var(--space-14); }
.gap-x-16 { column-gap: var(--space-16); }
.gap-x-20 { column-gap: var(--space-20); }
.gap-x-24 { column-gap: var(--space-24); }
.gap-x-28 { column-gap: var(--space-28); }
.gap-x-32 { column-gap: var(--space-32); }

/* Gap Y */
.gap-y-0 { row-gap: 0; }
.gap-y-px { row-gap: var(--space-px); }
.gap-y-0-5 { row-gap: var(--space-0-5); }
.gap-y-1 { row-gap: var(--space-1); }
.gap-y-1-5 { row-gap: var(--space-1-5); }
.gap-y-2 { row-gap: var(--space-2); }
.gap-y-2-5 { row-gap: var(--space-2-5); }
.gap-y-3 { row-gap: var(--space-3); }
.gap-y-3-5 { row-gap: var(--space-3-5); }
.gap-y-4 { row-gap: var(--space-4); }
.gap-y-5 { row-gap: var(--space-5); }
.gap-y-6 { row-gap: var(--space-6); }
.gap-y-7 { row-gap: var(--space-7); }
.gap-y-8 { row-gap: var(--space-8); }
.gap-y-9 { row-gap: var(--space-9); }
.gap-y-10 { row-gap: var(--space-10); }
.gap-y-11 { row-gap: var(--space-11); }
.gap-y-12 { row-gap: var(--space-12); }
.gap-y-14 { row-gap: var(--space-14); }
.gap-y-16 { row-gap: var(--space-16); }
.gap-y-20 { row-gap: var(--space-20); }
.gap-y-24 { row-gap: var(--space-24); }
.gap-y-28 { row-gap: var(--space-28); }
.gap-y-32 { row-gap: var(--space-32); }

/* Responsive Breakpoints */
@media (min-width: 475px) {
  .xs\:container { max-width: var(--container-xs); }
  .xs\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .xs\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .xs\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .xs\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xs\:flex { display: flex; }
  .xs\:flex-col { flex-direction: column; }
  .xs\:flex-row { flex-direction: row; }
  .xs\:justify-center { justify-content: center; }
  .xs\:items-center { align-items: center; }
}

@media (min-width: 640px) {
  .sm\:container { max-width: var(--container-sm); }
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .sm\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .sm\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .sm\:flex { display: flex; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:justify-center { justify-content: center; }
  .sm\:justify-between { justify-content: space-between; }
  .sm\:items-center { align-items: center; }
  .sm\:gap-4 { gap: var(--space-4); }
  .sm\:gap-6 { gap: var(--space-6); }
  .sm\:gap-8 { gap: var(--space-8); }
}

@media (min-width: 768px) {
  .md\:container { max-width: var(--container-md); }
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .md\:grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
  .md\:grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
  .md\:flex { display: flex; }
  .md\:flex-col { flex-direction: column; }
  .md\:flex-row { flex-direction: row; }
  .md\:justify-center { justify-content: center; }
  .md\:justify-between { justify-content: space-between; }
  .md\:items-center { align-items: center; }
  .md\:gap-4 { gap: var(--space-4); }
  .md\:gap-6 { gap: var(--space-6); }
  .md\:gap-8 { gap: var(--space-8); }
  .md\:gap-12 { gap: var(--space-12); }
}

@media (min-width: 1024px) {
  .lg\:container { max-width: var(--container-lg); }
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .lg\:grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
  .lg\:grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
  .lg\:grid-cols-9 { grid-template-columns: repeat(9, minmax(0, 1fr)); }
  .lg\:grid-cols-10 { grid-template-columns: repeat(10, minmax(0, 1fr)); }
  .lg\:grid-cols-11 { grid-template-columns: repeat(11, minmax(0, 1fr)); }
  .lg\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
  .lg\:flex { display: flex; }
  .lg\:flex-col { flex-direction: column; }
  .lg\:flex-row { flex-direction: row; }
  .lg\:justify-center { justify-content: center; }
  .lg\:justify-between { justify-content: space-between; }
  .lg\:items-center { align-items: center; }
  .lg\:gap-4 { gap: var(--space-4); }
  .lg\:gap-6 { gap: var(--space-6); }
  .lg\:gap-8 { gap: var(--space-8); }
  .lg\:gap-12 { gap: var(--space-12); }
  .lg\:gap-16 { gap: var(--space-16); }
}

@media (min-width: 1280px) {
  .xl\:container { max-width: var(--container-xl); }
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .xl\:grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
  .xl\:grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
  .xl\:grid-cols-9 { grid-template-columns: repeat(9, minmax(0, 1fr)); }
  .xl\:grid-cols-10 { grid-template-columns: repeat(10, minmax(0, 1fr)); }
  .xl\:grid-cols-11 { grid-template-columns: repeat(11, minmax(0, 1fr)); }
  .xl\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
  .xl\:flex { display: flex; }
  .xl\:flex-col { flex-direction: column; }
  .xl\:flex-row { flex-direction: row; }
  .xl\:justify-center { justify-content: center; }
  .xl\:justify-between { justify-content: space-between; }
  .xl\:items-center { align-items: center; }
  .xl\:gap-4 { gap: var(--space-4); }
  .xl\:gap-6 { gap: var(--space-6); }
  .xl\:gap-8 { gap: var(--space-8); }
  .xl\:gap-12 { gap: var(--space-12); }
  .xl\:gap-16 { gap: var(--space-16); }
  .xl\:gap-20 { gap: var(--space-20); }
}

@media (min-width: 1536px) {
  .\32xl\:container { max-width: var(--container-2xl); }
  .\32xl\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .\32xl\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .\32xl\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .\32xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .\32xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .\32xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .\32xl\:grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
  .\32xl\:grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
  .\32xl\:grid-cols-9 { grid-template-columns: repeat(9, minmax(0, 1fr)); }
  .\32xl\:grid-cols-10 { grid-template-columns: repeat(10, minmax(0, 1fr)); }
  .\32xl\:grid-cols-11 { grid-template-columns: repeat(11, minmax(0, 1fr)); }
  .\32xl\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
  .\32xl\:flex { display: flex; }
  .\32xl\:flex-col { flex-direction: column; }
  .\32xl\:flex-row { flex-direction: row; }
  .\32xl\:justify-center { justify-content: center; }
  .\32xl\:justify-between { justify-content: space-between; }
  .\32xl\:items-center { align-items: center; }
  .\32xl\:gap-4 { gap: var(--space-4); }
  .\32xl\:gap-6 { gap: var(--space-6); }
  .\32xl\:gap-8 { gap: var(--space-8); }
  .\32xl\:gap-12 { gap: var(--space-12); }
  .\32xl\:gap-16 { gap: var(--space-16); }
  .\32xl\:gap-20 { gap: var(--space-20); }
  .\32xl\:gap-24 { gap: var(--space-24); }
}
