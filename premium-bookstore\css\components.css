/* ===================================
   PREMIUM COMPONENTS
   Advanced UI Components
   =================================== */

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-all);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

[data-theme="dark"] .navbar {
    background: rgba(23, 23, 23, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) 0;
}

.navbar-brand .logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    transition: var(--transition-transform);
}

.logo-text {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: var(--font-serif);
}

.navbar-menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    list-style: none;
}

.nav-link {
    position: relative;
    padding: var(--space-2) var(--space-4);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-all);
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.1;
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before {
    left: 0;
}

.nav-link:hover {
    color: var(--primary-600);
    transform: translateY(-1px);
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.search-btn,
.theme-toggle,
.cart-btn {
    position: relative;
    width: 44px;
    height: 44px;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-all);
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover,
.theme-toggle:hover,
.cart-btn:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--secondary-500);
    color: white;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0);
    transition: transform 0.2s var(--ease-bounce);
}

.cart-count:not(:empty) {
    transform: scale(1);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: var(--transition-all);
}

/* Buttons */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-all);
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-large {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-base);
}

/* Hero Components */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    font-size: var(--font-size-sm);
    color: white;
    margin-bottom: var(--space-6);
}

.badge-icon {
    font-size: var(--font-size-lg);
}

.title-highlight {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-black);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-6);
    margin: var(--space-8) 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-black);
    color: #ffd700;
    font-family: var(--font-mono);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-top: var(--space-1);
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: var(--space-8);
}

.hero-features {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    color: white;
    opacity: 0.9;
}

/* Device Mockup */
.device-mockup {
    position: relative;
    width: 320px;
    height: 640px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 40px;
    padding: 20px;
    box-shadow: var(--shadow-2xl);
    border: 3px solid rgba(255, 255, 255, 0.1);
    transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.device-mockup:hover {
    transform: perspective(1000px) rotateY(-10deg) rotateX(2deg) scale(1.02);
}

.device-screen {
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    border-radius: 30px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.1);
}

.reading-interface {
    padding: var(--space-6);
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.interface-header {
    margin-bottom: var(--space-6);
}

.book-info h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.book-info p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.reading-progress {
    margin-top: var(--space-4);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--space-2);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

.progress-text {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.reading-content {
    flex: 1;
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    font-family: var(--font-serif);
}

.interface-controls {
    display: flex;
    gap: var(--space-3);
    justify-content: center;
    margin-top: var(--space-6);
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-all);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.control-btn:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.control-btn[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--neutral-900);
    color: white;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    z-index: 1000;
}

/* Floating UI Elements */
.floating-ui-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.ui-element {
    position: absolute;
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-4);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-primary);
    pointer-events: auto;
    transition: var(--transition-all);
}

.ui-element:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.notification {
    top: 20%;
    right: -200px;
    width: 280px;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.notification-icon {
    font-size: var(--font-size-2xl);
}

.notification-content h5 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.notification-content p {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.stats-card {
    bottom: 20%;
    left: -180px;
    width: 200px;
}

.stats-card h5 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
}

.stats-grid .stat {
    text-align: center;
}

.stats-grid .stat-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-600);
    line-height: 1;
}

.stats-grid .stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--space-1);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: var(--space-8);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    color: white;
    opacity: 0.8;
    cursor: pointer;
    transition: var(--transition-all);
}

.scroll-indicator:hover {
    opacity: 1;
    transform: translateX(-50%) translateY(-4px);
}

.scroll-arrow {
    animation: bounce-vertical 2s ease-in-out infinite;
}

.scroll-indicator span {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

/* Animations */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes bounce-vertical {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .navbar-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }
    
    .device-mockup {
        width: 280px;
        height: 560px;
        transform: none;
    }
    
    .floating-ui-elements {
        display: none;
    }
}

@media (max-width: 768px) {
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
    }
    
    .navbar-actions .btn {
        display: none;
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
    }
}

/* Advanced Features Section */
.features-advanced {
    padding: var(--space-32) 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.features-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(99,102,241,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.features-showcase {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-16);
    margin-bottom: var(--space-20);
}

.feature-card {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--space-8);
    align-items: center;
    padding: var(--space-8);
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-primary);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99,102,241,0.1), transparent);
    transition: left 0.6s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.feature-icon {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* AI Brain Animation */
.ai-brain {
    position: relative;
    width: 80px;
    height: 80px;
}

.brain-core {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.neural-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.neuron {
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--secondary-500);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

.neuron:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.neuron:nth-child(2) {
    top: 20%;
    right: 20%;
    animation-delay: 0.5s;
}

.neuron:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 1s;
}

.neuron:nth-child(4) {
    bottom: 20%;
    right: 20%;
    animation-delay: 1.5s;
}

/* VR Headset */
.vr-headset {
    position: relative;
    width: 80px;
    height: 50px;
}

.headset-body {
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 25px;
    position: relative;
}

.headset-lens {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #4ecdc4, #44a08d);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    animation: glow 2s ease-in-out infinite;
}

.headset-lens.left {
    left: 15px;
}

.headset-lens.right {
    right: 15px;
}

/* Analytics Chart */
.analytics-chart {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    height: 60px;
    width: 80px;
}

.chart-bar {
    flex: 1;
    background: var(--gradient-primary);
    border-radius: 4px 4px 0 0;
    animation: chartGrow 2s ease-out infinite;
}

.chart-bar:nth-child(1) { animation-delay: 0s; }
.chart-bar:nth-child(2) { animation-delay: 0.2s; }
.chart-bar:nth-child(3) { animation-delay: 0.4s; }
.chart-bar:nth-child(4) { animation-delay: 0.6s; }
.chart-bar:nth-child(5) { animation-delay: 0.8s; }

@keyframes chartGrow {
    0%, 50% {
        transform: scaleY(0.3);
    }
    100% {
        transform: scaleY(1);
    }
}

.feature-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.feature-content p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--space-6);
}

.feature-list {
    list-style: none;
    margin-bottom: var(--space-6);
}

.feature-list li {
    position: relative;
    padding: var(--space-2) 0;
    padding-right: var(--space-6);
    color: var(--text-secondary);
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    top: var(--space-2);
    color: var(--success);
    font-weight: var(--font-weight-bold);
}

/* Interactive Demo */
.interactive-demo {
    margin-top: var(--space-20);
}

.demo-container {
    max-width: 800px;
    margin: 0 auto;
}

.demo-screen {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    border: 1px solid var(--border-primary);
}

.demo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.demo-controls {
    display: flex;
    gap: var(--space-2);
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ff5f56; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #27ca3f; }

.demo-header h4 {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.demo-content {
    display: grid;
    grid-template-columns: 200px 1fr;
    min-height: 300px;
}

.demo-sidebar {
    background: var(--bg-secondary);
    padding: var(--space-4);
    border-left: 1px solid var(--border-primary);
}

.sidebar-item {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-all);
    margin-bottom: var(--space-2);
}

.sidebar-item.active,
.sidebar-item:hover {
    background: var(--primary-600);
    color: white;
}

.demo-main {
    padding: var(--space-6);
}

.demo-book-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
}

.demo-book {
    aspect-ratio: 3/4;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    cursor: pointer;
}

.demo-book:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.demo-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-6);
}

.demo-feature {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.feature-icon-small {
    font-size: var(--font-size-xl);
}

/* Performance Stats */
.performance-stats {
    padding: var(--space-20) 0;
    background: var(--gradient-primary);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
}

.stat-card {
    text-align: center;
    padding: var(--space-8);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-all);
}

.stat-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-4);
}

.stat-number {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-black);
    color: #ffd700;
    font-family: var(--font-mono);
    line-height: 1;
    margin-bottom: var(--space-2);
}

.stat-label {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-2);
}

.stat-description {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

@media (max-width: 1024px) {
    .feature-card {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-6);
    }

    .demo-content {
        grid-template-columns: 1fr;
    }

    .demo-sidebar {
        border-left: none;
        border-bottom: 1px solid var(--border-primary);
    }

    .demo-book-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .features-showcase {
        gap: var(--space-8);
    }

    .feature-card {
        padding: var(--space-6);
    }

    .demo-book-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }
}

@media (max-width: 640px) {
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }

    .device-mockup {
        width: 240px;
        height: 480px;
    }

    .reading-interface {
        padding: var(--space-4);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .demo-features {
        grid-template-columns: 1fr;
    }
}
