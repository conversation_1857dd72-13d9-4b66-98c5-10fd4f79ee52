<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="BookVerse Premium - أكبر مكتبة رقمية في الشرق الأوسط مع أكثر من مليون كتاب وتجربة قراءة ثورية">
    <meta name="keywords" content="كتب, مكتبة رقمية, قراءة, كتب عربية, BookVerse">
    <meta name="author" content="BookVerse Team">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="BookVerse Premium - عالم الكتب الرقمية">
    <meta property="og:description" content="اكتشف أكثر من مليون كتاب رقمي مع تجربة قراءة لا مثيل لها">
    <meta property="og:image" content="/images/og-image.jpg">
    <meta property="og:url" content="https://bookverse.com">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="BookVerse Premium - عالم الكتب الرقمية">
    <meta name="twitter:description" content="اكتشف أكثر من مليون كتاب رقمي مع تجربة قراءة لا مثيل لها">
    <meta name="twitter:image" content="/images/twitter-card.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Critical CSS -->
    <style>
        /* Critical above-the-fold styles */
        :root {
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --neutral-50: #fafafa;
            --neutral-900: #171717;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --font-arabic: 'Tajawal', system-ui, sans-serif;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            --radius-2xl: 1rem;
            --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        body {
            font-family: var(--font-arabic);
            line-height: 1.6;
            color: var(--neutral-900);
            background: var(--neutral-50);
            overflow-x: hidden;
        }
        
        .hero {
            min-height: 100vh;
            background: var(--gradient-primary);
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .hero-content {
            position: relative;
            z-index: 10;
            color: white;
            text-align: center;
        }
        
        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 800;
            margin-bottom: var(--space-6);
            line-height: 1.1;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: var(--space-8);
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-4) var(--space-8);
            border: none;
            border-radius: var(--radius-2xl);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition-all);
            cursor: pointer;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }
        
        .loader {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/core.css">
    <link rel="stylesheet" href="css/grid.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Title -->
    <title>BookVerse Premium - عالم الكتب الرقمية | أكبر مكتبة إلكترونية في الشرق الأوسط</title>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loader-container">
            <div class="loader"></div>
            <h2 style="color: white; margin-top: 2rem; font-family: var(--font-arabic);">BookVerse Premium</h2>
            <p style="color: rgba(255,255,255,0.8); margin-top: 0.5rem;">جاري تحميل عالم المعرفة...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" data-animate="fadeInDown">
        <div class="container">
            <div class="navbar-content">
                <div class="navbar-brand">
                    <div class="logo" data-magnetic="0.3">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="40" height="40" rx="8" fill="url(#logoGradient)"/>
                            <path d="M12 10h16c1.1 0 2 .9 2 2v16c0 1.1-.9 2-2 2H12c-1.1 0-2-.9-2-2V12c0-1.1.9-2 2-2z" fill="white" opacity="0.9"/>
                            <path d="M16 14h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" fill="url(#logoGradient)"/>
                            <defs>
                                <linearGradient id="logoGradient" x1="0" y1="0" x2="40" y2="40">
                                    <stop offset="0%" stop-color="#667eea"/>
                                    <stop offset="100%" stop-color="#764ba2"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        <span class="logo-text">BookVerse</span>
                    </div>
                </div>
                
                <div class="navbar-menu">
                    <a href="#home" class="nav-link" data-magnetic="0.2">الرئيسية</a>
                    <a href="#library" class="nav-link" data-magnetic="0.2">المكتبة</a>
                    <a href="#categories" class="nav-link" data-magnetic="0.2">التصنيفات</a>
                    <a href="#features" class="nav-link" data-magnetic="0.2">المميزات</a>
                    <a href="#pricing" class="nav-link" data-magnetic="0.2">الأسعار</a>
                </div>
                
                <div class="navbar-actions">
                    <button class="search-btn" data-magnetic="0.3" aria-label="البحث">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </button>
                    
                    <button class="theme-toggle" data-magnetic="0.3" aria-label="تبديل الوضع">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                        </svg>
                    </button>
                    
                    <button class="cart-btn" data-magnetic="0.3" aria-label="السلة">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"/>
                        </svg>
                        <span class="cart-count">0</span>
                    </button>
                    
                    <button class="btn btn-primary">تسجيل الدخول</button>
                </div>
                
                <button class="mobile-menu-toggle" aria-label="القائمة">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero" data-parallax-layer="1">
        <div class="hero-background">
            <div class="particle-field" id="particleField"></div>
            <div class="floating-elements">
                <div class="floating-book" data-parallax="0.2">📚</div>
                <div class="floating-book" data-parallax="0.3">📖</div>
                <div class="floating-book" data-parallax="0.4">📝</div>
                <div class="floating-book" data-parallax="0.5">📜</div>
                <div class="floating-book" data-parallax="0.6">📋</div>
            </div>
        </div>
        
        <div class="container">
            <div class="hero-content" data-animate="fadeInUp">
                <div class="hero-badge" data-animate="fadeInUp" data-delay="200">
                    <span class="badge-icon">🏆</span>
                    <span>الأكثر ثقة في المنطقة - أكثر من 2.5 مليون مستخدم</span>
                </div>
                
                <h1 class="hero-title" data-animate="fadeInUp" data-delay="400">
                    <span class="title-line">عالم لا محدود من</span>
                    <span class="title-highlight">المعرفة والإبداع</span>
                </h1>
                
                <p class="hero-subtitle" data-animate="fadeInUp" data-delay="600">
                    اكتشف أكثر من <strong>مليون كتاب رقمي</strong> في جميع المجالات مع تجربة قراءة ثورية مدعومة بالذكاء الاصطناعي. 
                    قراءة فورية، تحميل مجاني، ومزامنة عبر جميع أجهزتك.
                </p>
                
                <div class="hero-stats" data-animate="fadeInUp" data-delay="800">
                    <div class="stat-item">
                        <span class="stat-number" data-count="1000000">0</span>
                        <span class="stat-label">كتاب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="50000">0</span>
                        <span class="stat-label">مؤلف</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="2500000">0</span>
                        <span class="stat-label">قارئ نشط</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="150">0</span>
                        <span class="stat-label">دولة</span>
                    </div>
                </div>
                
                <div class="hero-actions" data-animate="fadeInUp" data-delay="1000">
                    <a href="#library" class="btn btn-primary btn-large" data-magnetic="0.3">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        ابدأ القراءة الآن
                    </a>
                    
                    <button class="btn btn-secondary btn-large" data-magnetic="0.3" id="playDemoBtn">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1M9 10v5a2 2 0 002 2h2a2 2 0 002-2v-5"/>
                        </svg>
                        شاهد العرض التوضيحي
                    </button>
                </div>
                
                <div class="hero-features" data-animate="fadeInUp" data-delay="1200">
                    <div class="feature-badge">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                        <span>تشفير متقدم</span>
                    </div>
                    <div class="feature-badge">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                        <span>مجاني للأبد</span>
                    </div>
                    <div class="feature-badge">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        <span>سرعة فائقة</span>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual" data-animate="fadeInRight" data-delay="600">
                <div class="device-mockup" data-parallax="0.1">
                    <div class="device-screen">
                        <div class="reading-interface">
                            <div class="interface-header">
                                <div class="book-info">
                                    <h4>الأسود يليق بك</h4>
                                    <p>أحلام مستغانمي</p>
                                </div>
                                <div class="reading-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 68%"></div>
                                    </div>
                                    <span class="progress-text">68% مكتمل</span>
                                </div>
                            </div>
                            
                            <div class="reading-content">
                                <p>"في عالم يتسارع فيه الزمن، تبقى الكتب هي النافذة الوحيدة التي تأخذنا إلى عوالم لا محدودة من المعرفة والخيال..."</p>
                            </div>
                            
                            <div class="interface-controls">
                                <button class="control-btn" data-tooltip="إضافة إشارة مرجعية">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                    </svg>
                                </button>
                                <button class="control-btn" data-tooltip="تمييز النص">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4"/>
                                    </svg>
                                </button>
                                <button class="control-btn" data-tooltip="مشاركة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="floating-ui-elements">
                    <div class="ui-element notification" data-animate="fadeInUp" data-delay="1400">
                        <div class="notification-icon">📚</div>
                        <div class="notification-content">
                            <h5>كتاب جديد متاح!</h5>
                            <p>تم إضافة "العادات الذرية" إلى مكتبتك</p>
                        </div>
                    </div>
                    
                    <div class="ui-element stats-card" data-animate="fadeInUp" data-delay="1600">
                        <h5>إحصائيات القراءة</h5>
                        <div class="stats-grid">
                            <div class="stat">
                                <span class="stat-value">12</span>
                                <span class="stat-label">كتاب هذا الشهر</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">45</span>
                                <span class="stat-label">ساعة قراءة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="scroll-indicator" data-animate="fadeInUp" data-delay="1800">
            <div class="scroll-arrow">
                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                </svg>
            </div>
            <span>اكتشف المزيد</span>
        </div>
    </section>

    <!-- Advanced Features Section -->
    <section id="features" class="features-advanced" data-parallax-layer="2">
        <div class="container">
            <div class="section-header" data-animate="fadeInUp">
                <h2 class="section-title">تقنيات متطورة لتجربة قراءة استثنائية</h2>
                <p class="section-subtitle">اكتشف مميزات BookVerse التي تجعلها الخيار الأول للقراء في العالم العربي</p>
            </div>

            <div class="features-showcase">
                <!-- AI Reading Assistant -->
                <div class="feature-card ai-feature" data-animate="scaleInBounce" data-delay="200">
                    <div class="feature-icon">
                        <div class="ai-brain">
                            <div class="brain-core"></div>
                            <div class="neural-network">
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                            </div>
                        </div>
                    </div>
                    <div class="feature-content">
                        <h3>مساعد القراءة الذكي</h3>
                        <p>ذكاء اصطناعي متطور يتعلم من عادات قراءتك ويقترح الكتب المناسبة لك بدقة 95%</p>
                        <ul class="feature-list">
                            <li>توصيات شخصية ذكية</li>
                            <li>تحليل أسلوب القراءة</li>
                            <li>اقتراحات بناءً على المزاج</li>
                        </ul>
                        <button class="btn btn-primary" data-magnetic="0.3">جرب الآن</button>
                    </div>
                </div>

                <!-- 3D Virtual Library -->
                <div class="feature-card vr-feature" data-animate="fadeInLeft" data-delay="400">
                    <div class="feature-icon">
                        <div class="vr-headset">
                            <div class="headset-body"></div>
                            <div class="headset-lens left"></div>
                            <div class="headset-lens right"></div>
                        </div>
                    </div>
                    <div class="feature-content">
                        <h3>مكتبة ثلاثية الأبعاد</h3>
                        <p>تجول في مكتبة افتراضية مذهلة واستكشف الكتب بطريقة تفاعلية لم تشهدها من قبل</p>
                        <ul class="feature-list">
                            <li>تجربة غامرة بتقنية WebGL</li>
                            <li>تفاعل طبيعي مع الكتب</li>
                            <li>بيئات قراءة متنوعة</li>
                        </ul>
                        <button class="btn btn-secondary" data-magnetic="0.3" onclick="window.BookViewer3D?.toggle3DViewer()">
                            <span>استكشف المكتبة</span>
                            <small>(اضغط Ctrl+3)</small>
                        </button>
                    </div>
                </div>

                <!-- Smart Reading Analytics -->
                <div class="feature-card analytics-feature" data-animate="fadeInRight" data-delay="600">
                    <div class="feature-icon">
                        <div class="analytics-chart">
                            <div class="chart-bar" style="height: 60%"></div>
                            <div class="chart-bar" style="height: 80%"></div>
                            <div class="chart-bar" style="height: 45%"></div>
                            <div class="chart-bar" style="height: 90%"></div>
                            <div class="chart-bar" style="height: 70%"></div>
                        </div>
                    </div>
                    <div class="feature-content">
                        <h3>تحليلات القراءة الذكية</h3>
                        <p>احصل على رؤى عميقة حول عادات قراءتك وتقدمك مع تقارير تفصيلية وإحصائيات متقدمة</p>
                        <ul class="feature-list">
                            <li>تتبع سرعة القراءة</li>
                            <li>تحليل أوقات النشاط</li>
                            <li>إحصائيات شاملة</li>
                        </ul>
                        <button class="btn btn-primary" data-magnetic="0.3">عرض التحليلات</button>
                    </div>
                </div>
            </div>

            <!-- Interactive Demo -->
            <div class="interactive-demo" data-animate="fadeInUp" data-delay="800">
                <div class="demo-container">
                    <div class="demo-screen">
                        <div class="demo-header">
                            <div class="demo-controls">
                                <span class="control red"></span>
                                <span class="control yellow"></span>
                                <span class="control green"></span>
                            </div>
                            <h4>BookVerse Reader</h4>
                        </div>
                        <div class="demo-content">
                            <div class="demo-sidebar">
                                <div class="sidebar-item active">📚 مكتبتي</div>
                                <div class="sidebar-item">⭐ المفضلة</div>
                                <div class="sidebar-item">📊 الإحصائيات</div>
                                <div class="sidebar-item">🎯 التوصيات</div>
                            </div>
                            <div class="demo-main">
                                <div class="demo-book-grid">
                                    <div class="demo-book" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #4ecdc4, #44a08d);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #45b7d1, #96c93d);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #f39c12, #f1c40f);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #9b59b6, #8e44ad);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #e74c3c, #c0392b);"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="demo-features">
                        <div class="demo-feature">
                            <div class="feature-icon-small">🚀</div>
                            <span>تحميل فوري</span>
                        </div>
                        <div class="demo-feature">
                            <div class="feature-icon-small">🔄</div>
                            <span>مزامنة تلقائية</span>
                        </div>
                        <div class="demo-feature">
                            <div class="feature-icon-small">📱</div>
                            <span>متوافق مع جميع الأجهزة</span>
                        </div>
                        <div class="demo-feature">
                            <div class="feature-icon-small">🔒</div>
                            <span>حماية متقدمة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Stats -->
    <section class="performance-stats">
        <div class="container">
            <div class="stats-grid" data-animate="fadeInUp">
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-number" data-count="99">0</div>
                    <div class="stat-label">نقاط الأداء</div>
                    <div class="stat-description">سرعة تحميل استثنائية</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🔒</div>
                    <div class="stat-number" data-count="100">0</div>
                    <div class="stat-label">% أمان</div>
                    <div class="stat-description">حماية بيانات متقدمة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🌍</div>
                    <div class="stat-number" data-count="150">0</div>
                    <div class="stat-label">دولة</div>
                    <div class="stat-description">انتشار عالمي</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-number" data-count="98">0</div>
                    <div class="stat-label">% رضا المستخدمين</div>
                    <div class="stat-description">تقييمات ممتازة</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Library Section -->
    <section id="library" class="library-section">
        <div class="container">
            <div class="section-header" data-animate="fadeInUp">
                <h2 class="section-title">استكشف مكتبتنا الضخمة</h2>
                <p class="section-subtitle">أكثر من مليون كتاب في جميع المجالات والتخصصات</p>
            </div>

            <!-- Book Categories -->
            <div class="categories-grid" data-animate="fadeInUp" data-delay="200">
                <div class="category-card" data-category="literature">
                    <div class="category-icon">📚</div>
                    <h3>الأدب والشعر</h3>
                    <p>أعمال أدبية خالدة من أشهر الكتاب العرب والعالميين</p>
                    <span class="book-count">25,000+ كتاب</span>
                </div>

                <div class="category-card" data-category="science">
                    <div class="category-icon">🔬</div>
                    <h3>العلوم والتكنولوجيا</h3>
                    <p>أحدث الاكتشافات والتطورات في عالم العلم والتقنية</p>
                    <span class="book-count">18,000+ كتاب</span>
                </div>

                <div class="category-card" data-category="business">
                    <div class="category-icon">💼</div>
                    <h3>الأعمال والاقتصاد</h3>
                    <p>استراتيجيات النجاح وأسرار ريادة الأعمال</p>
                    <span class="book-count">12,000+ كتاب</span>
                </div>

                <div class="category-card" data-category="history">
                    <div class="category-icon">🏛️</div>
                    <h3>التاريخ والحضارة</h3>
                    <p>رحلة عبر التاريخ لاستكشاف الحضارات القديمة</p>
                    <span class="book-count">15,000+ كتاب</span>
                </div>

                <div class="category-card" data-category="religion">
                    <div class="category-icon">🕌</div>
                    <h3>الدين والفلسفة</h3>
                    <p>كتب دينية وفلسفية تثري الروح والعقل</p>
                    <span class="book-count">20,000+ كتاب</span>
                </div>

                <div class="category-card" data-category="children">
                    <div class="category-icon">🧸</div>
                    <h3>كتب الأطفال</h3>
                    <p>قصص وحكايات ممتعة لتنمية خيال الأطفال</p>
                    <span class="book-count">8,000+ كتاب</span>
                </div>
            </div>

            <!-- Featured Books -->
            <div class="featured-books" data-animate="fadeInUp" data-delay="400">
                <h3>الكتب المميزة</h3>
                <div class="books-slider">
                    <div class="book-card" data-book-id="1">
                        <div class="book-cover">
                            <div class="book-image" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);">
                                <span class="book-title-overlay">الأسود يليق بك</span>
                            </div>
                            <div class="book-actions">
                                <button class="btn-icon favorite-btn" data-tooltip="إضافة للمفضلة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                </button>
                                <button class="btn-icon share-btn" data-tooltip="مشاركة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="book-info">
                            <h4>الأسود يليق بك</h4>
                            <p class="book-author">أحلام مستغانمي</p>
                            <div class="book-rating">
                                <div class="stars">
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star">★</span>
                                </div>
                                <span class="rating-text">4.8 (2,543 تقييم)</span>
                            </div>
                            <div class="book-price">
                                <span class="current-price">29 ريال</span>
                                <span class="old-price">45 ريال</span>
                                <span class="discount">-35%</span>
                            </div>
                            <button class="btn btn-primary add-to-cart" data-book-id="1">
                                إضافة للسلة
                            </button>
                        </div>
                    </div>

                    <div class="book-card" data-book-id="2">
                        <div class="book-cover">
                            <div class="book-image" style="background: linear-gradient(45deg, #4ecdc4, #44a08d);">
                                <span class="book-title-overlay">مئة عام من العزلة</span>
                            </div>
                            <div class="book-actions">
                                <button class="btn-icon favorite-btn" data-tooltip="إضافة للمفضلة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                </button>
                                <button class="btn-icon share-btn" data-tooltip="مشاركة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="book-info">
                            <h4>مئة عام من العزلة</h4>
                            <p class="book-author">غابرييل غارسيا ماركيز</p>
                            <div class="book-rating">
                                <div class="stars">
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                </div>
                                <span class="rating-text">4.9 (3,821 تقييم)</span>
                            </div>
                            <div class="book-price">
                                <span class="current-price">35 ريال</span>
                                <span class="old-price">50 ريال</span>
                                <span class="discount">-30%</span>
                            </div>
                            <button class="btn btn-primary add-to-cart" data-book-id="2">
                                إضافة للسلة
                            </button>
                        </div>
                    </div>

                    <div class="book-card" data-book-id="3">
                        <div class="book-cover">
                            <div class="book-image" style="background: linear-gradient(45deg, #45b7d1, #96c93d);">
                                <span class="book-title-overlay">العادات الذرية</span>
                            </div>
                            <div class="book-actions">
                                <button class="btn-icon favorite-btn" data-tooltip="إضافة للمفضلة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                </button>
                                <button class="btn-icon share-btn" data-tooltip="مشاركة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="book-info">
                            <h4>العادات الذرية</h4>
                            <p class="book-author">جيمس كلير</p>
                            <div class="book-rating">
                                <div class="stars">
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star filled">★</span>
                                    <span class="star">★</span>
                                </div>
                                <span class="rating-text">4.7 (1,892 تقييم)</span>
                            </div>
                            <div class="book-price">
                                <span class="current-price">25 ريال</span>
                                <span class="old-price">40 ريال</span>
                                <span class="discount">-37%</span>
                            </div>
                            <button class="btn btn-primary add-to-cart" data-book-id="3">
                                إضافة للسلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section">
        <div class="container">
            <div class="section-header" data-animate="fadeInUp">
                <h2 class="section-title">خطط اشتراك مرنة</h2>
                <p class="section-subtitle">اختر الخطة التي تناسب احتياجاتك في القراءة</p>
            </div>

            <div class="pricing-toggle" data-animate="fadeInUp" data-delay="200">
                <span class="toggle-label">شهري</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="pricingToggle">
                    <span class="toggle-slider"></span>
                </label>
                <span class="toggle-label">سنوي <span class="save-badge">وفر 30%</span></span>
            </div>

            <div class="pricing-cards" data-animate="fadeInUp" data-delay="400">
                <div class="pricing-card basic">
                    <div class="card-header">
                        <h3>الخطة الأساسية</h3>
                        <div class="price">
                            <span class="currency">ريال</span>
                            <span class="amount monthly">19</span>
                            <span class="amount yearly" style="display: none;">190</span>
                            <span class="period monthly">/شهر</span>
                            <span class="period yearly" style="display: none;">/سنة</span>
                        </div>
                        <p class="price-description">مثالية للقراء المبتدئين</p>
                    </div>
                    <div class="card-features">
                        <ul>
                            <li><span class="check">✓</span> 5 كتب شهرياً</li>
                            <li><span class="check">✓</span> مكتبة أساسية</li>
                            <li><span class="check">✓</span> قراءة على جهاز واحد</li>
                            <li><span class="check">✓</span> دعم فني أساسي</li>
                            <li><span class="cross">✗</span> تحميل للقراءة بدون إنترنت</li>
                            <li><span class="cross">✗</span> توصيات ذكية</li>
                        </ul>
                    </div>
                    <button class="btn btn-outline">اختر هذه الخطة</button>
                </div>

                <div class="pricing-card premium featured">
                    <div class="popular-badge">الأكثر شعبية</div>
                    <div class="card-header">
                        <h3>الخطة المميزة</h3>
                        <div class="price">
                            <span class="currency">ريال</span>
                            <span class="amount monthly">39</span>
                            <span class="amount yearly" style="display: none;">390</span>
                            <span class="period monthly">/شهر</span>
                            <span class="period yearly" style="display: none;">/سنة</span>
                        </div>
                        <p class="price-description">الأفضل للقراء النشطين</p>
                    </div>
                    <div class="card-features">
                        <ul>
                            <li><span class="check">✓</span> كتب غير محدودة</li>
                            <li><span class="check">✓</span> مكتبة كاملة</li>
                            <li><span class="check">✓</span> قراءة على 3 أجهزة</li>
                            <li><span class="check">✓</span> تحميل بدون إنترنت</li>
                            <li><span class="check">✓</span> توصيات ذكية</li>
                            <li><span class="check">✓</span> دعم فني متقدم</li>
                        </ul>
                    </div>
                    <button class="btn btn-primary">اختر هذه الخطة</button>
                </div>

                <div class="pricing-card family">
                    <div class="card-header">
                        <h3>الخطة العائلية</h3>
                        <div class="price">
                            <span class="currency">ريال</span>
                            <span class="amount monthly">59</span>
                            <span class="amount yearly" style="display: none;">590</span>
                            <span class="period monthly">/شهر</span>
                            <span class="period yearly" style="display: none;">/سنة</span>
                        </div>
                        <p class="price-description">مثالية للعائلات الكبيرة</p>
                    </div>
                    <div class="card-features">
                        <ul>
                            <li><span class="check">✓</span> كتب غير محدودة</li>
                            <li><span class="check">✓</span> مكتبة كاملة + حصرية</li>
                            <li><span class="check">✓</span> 6 حسابات منفصلة</li>
                            <li><span class="check">✓</span> قراءة على أجهزة غير محدودة</li>
                            <li><span class="check">✓</span> توصيات ذكية متقدمة</li>
                            <li><span class="check">✓</span> دعم فني مخصص 24/7</li>
                        </ul>
                    </div>
                    <button class="btn btn-outline">اختر هذه الخطة</button>
                </div>
            </div>

            <div class="pricing-guarantee" data-animate="fadeInUp" data-delay="600">
                <div class="guarantee-content">
                    <div class="guarantee-icon">🛡️</div>
                    <div class="guarantee-text">
                        <h4>ضمان استرداد المال</h4>
                        <p>إذا لم تكن راضياً عن الخدمة، يمكنك استرداد أموالك خلال 30 يوماً</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <div class="section-header" data-animate="fadeInUp">
                <h2 class="section-title">ماذا يقول عملاؤنا</h2>
                <p class="section-subtitle">آراء حقيقية من قراء يثقون في BookVerse</p>
            </div>

            <div class="testimonials-slider" data-animate="fadeInUp" data-delay="200">
                <div class="testimonial-card active">
                    <div class="testimonial-content">
                        <div class="quote-icon">"</div>
                        <p>"BookVerse غيّر طريقة قراءتي تماماً. التوصيات الذكية ساعدتني في اكتشاف كتب رائعة لم أكن لأجدها بنفسي. المكتبة ثلاثية الأبعاد تجربة مذهلة!"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='50' fill='%23667eea'/><text x='50' y='55' text-anchor='middle' fill='white' font-size='30' font-family='Arial'>أ</text></svg>" alt="أحمد محمد">
                        </div>
                        <div class="author-info">
                            <h4>أحمد محمد</h4>
                            <p>كاتب ومترجم</p>
                            <div class="rating">
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">"</div>
                        <p>"كأم لثلاثة أطفال، الخطة العائلية مثالية لنا. كل فرد في العائلة له حسابه الخاص مع توصيات مناسبة لعمره. خدمة العملاء ممتازة أيضاً."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='50' fill='%23f093fb'/><text x='50' y='55' text-anchor='middle' fill='white' font-size='30' font-family='Arial'>ف</text></svg>" alt="فاطمة العلي">
                        </div>
                        <div class="author-info">
                            <h4>فاطمة العلي</h4>
                            <p>أم وربة منزل</p>
                            <div class="rating">
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">"</div>
                        <p>"بصفتي طالب دراسات عليا، أحتاج لمراجع كثيرة. BookVerse وفر لي مكتبة ضخمة من الكتب الأكاديمية والبحثية. ميزة البحث المتقدم توفر علي ساعات من البحث."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='50' fill='%2345b7d1'/><text x='50' y='55' text-anchor='middle' fill='white' font-size='30' font-family='Arial'>م</text></svg>" alt="محمد الشريف">
                        </div>
                        <div class="author-info">
                            <h4>محمد الشريف</h4>
                            <p>طالب دكتوراه</p>
                            <div class="rating">
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                                <span class="star">★</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="testimonials-navigation">
                <button class="nav-btn prev-btn" data-direction="prev">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <div class="testimonials-dots">
                    <span class="dot active" data-slide="0"></span>
                    <span class="dot" data-slide="1"></span>
                    <span class="dot" data-slide="2"></span>
                </div>
                <button class="nav-btn next-btn" data-direction="next">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="newsletter-content" data-animate="fadeInUp">
                <div class="newsletter-text">
                    <h2>ابق على اطلاع بأحدث الكتب</h2>
                    <p>اشترك في نشرتنا الإخبارية واحصل على توصيات أسبوعية مخصصة لك</p>
                </div>
                <form class="newsletter-form" id="newsletterForm">
                    <div class="form-group">
                        <input type="email" placeholder="أدخل بريدك الإلكتروني" required>
                        <button type="submit" class="btn btn-primary">
                            اشترك الآن
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                        </button>
                    </div>
                    <p class="privacy-note">
                        <svg width="12" height="12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                        نحن نحترم خصوصيتك ولن نشارك بياناتك مع أطراف ثالثة
                    </p>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section brand">
                    <div class="footer-logo">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="40" height="40" rx="8" fill="url(#footerGradient)"/>
                            <path d="M12 10h16c1.1 0 2 .9 2 2v16c0 1.1-.9 2-2 2H12c-1.1 0-2-.9-2-2V12c0-1.1.9-2 2-2z" fill="white" opacity="0.9"/>
                            <path d="M16 14h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" fill="url(#footerGradient)"/>
                            <defs>
                                <linearGradient id="footerGradient" x1="0" y1="0" x2="40" y2="40">
                                    <stop offset="0%" stop-color="#667eea"/>
                                    <stop offset="100%" stop-color="#764ba2"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        <span>BookVerse Premium</span>
                    </div>
                    <p class="footer-description">
                        منصة القراءة الرقمية الرائدة في الشرق الأوسط. نوفر أكثر من مليون كتاب رقمي مع تجربة قراءة استثنائية مدعومة بالذكاء الاصطناعي.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link" data-tooltip="فيسبوك">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" data-tooltip="تويتر">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" data-tooltip="إنستغرام">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.384 0-.735-.147-.997-.42-.262-.262-.42-.613-.42-.997 0-.384.158-.735.42-.997.262-.262.613-.42.997-.42s.735.158.997.42c.262.262.42.613.42.997 0 .384-.158.735-.42.997-.262.273-.613.42-.997.42z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" data-tooltip="لينكد إن">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="#home">الرئيسية</a></li>
                        <li><a href="#library">المكتبة</a></li>
                        <li><a href="#categories">التصنيفات</a></li>
                        <li><a href="#features">المميزات</a></li>
                        <li><a href="#pricing">الأسعار</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>الدعم والمساعدة</h4>
                    <ul class="footer-links">
                        <li><a href="#">مركز المساعدة</a></li>
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">تواصل معنا</a></li>
                        <li><a href="#">الدعم الفني</a></li>
                        <li><a href="#">تقرير مشكلة</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>الشركة</h4>
                    <ul class="footer-links">
                        <li><a href="#">من نحن</a></li>
                        <li><a href="#">فريق العمل</a></li>
                        <li><a href="#">الوظائف</a></li>
                        <li><a href="#">الأخبار</a></li>
                        <li><a href="#">المستثمرون</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>القانونية</h4>
                    <ul class="footer-links">
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">سياسة الاسترداد</a></li>
                        <li><a href="#">حقوق الطبع والنشر</a></li>
                        <li><a href="#">اتفاقية الترخيص</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; 2024 BookVerse Premium. جميع الحقوق محفوظة.</p>
                    <div class="footer-badges">
                        <span class="badge">🔒 آمن ومشفر</span>
                        <span class="badge">🌍 متاح عالمياً</span>
                        <span class="badge">📱 متوافق مع جميع الأجهزة</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="js/performance-optimizer.js"></script>
    <script src="js/webgl-effects.js"></script>
    <script src="js/advanced-animations.js"></script>
    <script src="js/3d-book-viewer.js"></script>
    <script src="js/ai-recommendations.js"></script>
    <script src="js/main.js"></script></script>
    
    <!-- Analytics -->
    <script>
        // Performance monitoring
        window.addEventListener('load', () => {
            // Hide loading screen
            const loadingScreen = document.getElementById('loadingScreen');
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
            }, 1500);
            
            // Track page load time
            const loadTime = performance.now();
            console.log(`🚀 Page loaded in ${Math.round(loadTime)}ms`);
        });
    </script>
</body>
</html>
