<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="BookVerse Premium - أكبر مكتبة رقمية في الشرق الأوسط مع أكثر من مليون كتاب وتجربة قراءة ثورية">
    <meta name="keywords" content="كتب, مكتبة رقمية, قراءة, كتب عربية, BookVerse">
    <meta name="author" content="BookVerse Team">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="BookVerse Premium - عالم الكتب الرقمية">
    <meta property="og:description" content="اكتشف أكثر من مليون كتاب رقمي مع تجربة قراءة لا مثيل لها">
    <meta property="og:image" content="/images/og-image.jpg">
    <meta property="og:url" content="https://bookverse.com">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="BookVerse Premium - عالم الكتب الرقمية">
    <meta name="twitter:description" content="اكتشف أكثر من مليون كتاب رقمي مع تجربة قراءة لا مثيل لها">
    <meta name="twitter:image" content="/images/twitter-card.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Critical CSS -->
    <style>
        /* Critical above-the-fold styles */
        :root {
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --neutral-50: #fafafa;
            --neutral-900: #171717;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --font-arabic: 'Tajawal', system-ui, sans-serif;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            --radius-2xl: 1rem;
            --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        body {
            font-family: var(--font-arabic);
            line-height: 1.6;
            color: var(--neutral-900);
            background: var(--neutral-50);
            overflow-x: hidden;
        }
        
        .hero {
            min-height: 100vh;
            background: var(--gradient-primary);
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .hero-content {
            position: relative;
            z-index: 10;
            color: white;
            text-align: center;
        }
        
        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 800;
            margin-bottom: var(--space-6);
            line-height: 1.1;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: var(--space-8);
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-4) var(--space-8);
            border: none;
            border-radius: var(--radius-2xl);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition-all);
            cursor: pointer;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }
        
        .loader {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/core.css">
    <link rel="stylesheet" href="css/grid.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Title -->
    <title>BookVerse Premium - عالم الكتب الرقمية | أكبر مكتبة إلكترونية في الشرق الأوسط</title>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loader-container">
            <div class="loader"></div>
            <h2 style="color: white; margin-top: 2rem; font-family: var(--font-arabic);">BookVerse Premium</h2>
            <p style="color: rgba(255,255,255,0.8); margin-top: 0.5rem;">جاري تحميل عالم المعرفة...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" data-animate="fadeInDown">
        <div class="container">
            <div class="navbar-content">
                <div class="navbar-brand">
                    <div class="logo" data-magnetic="0.3">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="40" height="40" rx="8" fill="url(#logoGradient)"/>
                            <path d="M12 10h16c1.1 0 2 .9 2 2v16c0 1.1-.9 2-2 2H12c-1.1 0-2-.9-2-2V12c0-1.1.9-2 2-2z" fill="white" opacity="0.9"/>
                            <path d="M16 14h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" fill="url(#logoGradient)"/>
                            <defs>
                                <linearGradient id="logoGradient" x1="0" y1="0" x2="40" y2="40">
                                    <stop offset="0%" stop-color="#667eea"/>
                                    <stop offset="100%" stop-color="#764ba2"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        <span class="logo-text">BookVerse</span>
                    </div>
                </div>
                
                <div class="navbar-menu">
                    <a href="#home" class="nav-link" data-magnetic="0.2">الرئيسية</a>
                    <a href="#library" class="nav-link" data-magnetic="0.2">المكتبة</a>
                    <a href="#categories" class="nav-link" data-magnetic="0.2">التصنيفات</a>
                    <a href="#features" class="nav-link" data-magnetic="0.2">المميزات</a>
                    <a href="#pricing" class="nav-link" data-magnetic="0.2">الأسعار</a>
                </div>
                
                <div class="navbar-actions">
                    <button class="search-btn" data-magnetic="0.3" aria-label="البحث">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </button>
                    
                    <button class="theme-toggle" data-magnetic="0.3" aria-label="تبديل الوضع">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                        </svg>
                    </button>
                    
                    <button class="cart-btn" data-magnetic="0.3" aria-label="السلة">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"/>
                        </svg>
                        <span class="cart-count">0</span>
                    </button>
                    
                    <button class="btn btn-primary">تسجيل الدخول</button>
                </div>
                
                <button class="mobile-menu-toggle" aria-label="القائمة">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero" data-parallax-layer="1">
        <div class="hero-background">
            <div class="particle-field" id="particleField"></div>
            <div class="floating-elements">
                <div class="floating-book" data-parallax="0.2">📚</div>
                <div class="floating-book" data-parallax="0.3">📖</div>
                <div class="floating-book" data-parallax="0.4">📝</div>
                <div class="floating-book" data-parallax="0.5">📜</div>
                <div class="floating-book" data-parallax="0.6">📋</div>
            </div>
        </div>
        
        <div class="container">
            <div class="hero-content" data-animate="fadeInUp">
                <div class="hero-badge" data-animate="fadeInUp" data-delay="200">
                    <span class="badge-icon">🏆</span>
                    <span>الأكثر ثقة في المنطقة - أكثر من 2.5 مليون مستخدم</span>
                </div>
                
                <h1 class="hero-title" data-animate="fadeInUp" data-delay="400">
                    <span class="title-line">عالم لا محدود من</span>
                    <span class="title-highlight">المعرفة والإبداع</span>
                </h1>
                
                <p class="hero-subtitle" data-animate="fadeInUp" data-delay="600">
                    اكتشف أكثر من <strong>مليون كتاب رقمي</strong> في جميع المجالات مع تجربة قراءة ثورية مدعومة بالذكاء الاصطناعي. 
                    قراءة فورية، تحميل مجاني، ومزامنة عبر جميع أجهزتك.
                </p>
                
                <div class="hero-stats" data-animate="fadeInUp" data-delay="800">
                    <div class="stat-item">
                        <span class="stat-number" data-count="1000000">0</span>
                        <span class="stat-label">كتاب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="50000">0</span>
                        <span class="stat-label">مؤلف</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="2500000">0</span>
                        <span class="stat-label">قارئ نشط</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="150">0</span>
                        <span class="stat-label">دولة</span>
                    </div>
                </div>
                
                <div class="hero-actions" data-animate="fadeInUp" data-delay="1000">
                    <a href="#library" class="btn btn-primary btn-large" data-magnetic="0.3">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        ابدأ القراءة الآن
                    </a>
                    
                    <button class="btn btn-secondary btn-large" data-magnetic="0.3" id="playDemoBtn">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1M9 10v5a2 2 0 002 2h2a2 2 0 002-2v-5"/>
                        </svg>
                        شاهد العرض التوضيحي
                    </button>
                </div>
                
                <div class="hero-features" data-animate="fadeInUp" data-delay="1200">
                    <div class="feature-badge">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                        <span>تشفير متقدم</span>
                    </div>
                    <div class="feature-badge">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                        <span>مجاني للأبد</span>
                    </div>
                    <div class="feature-badge">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        <span>سرعة فائقة</span>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual" data-animate="fadeInRight" data-delay="600">
                <div class="device-mockup" data-parallax="0.1">
                    <div class="device-screen">
                        <div class="reading-interface">
                            <div class="interface-header">
                                <div class="book-info">
                                    <h4>الأسود يليق بك</h4>
                                    <p>أحلام مستغانمي</p>
                                </div>
                                <div class="reading-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 68%"></div>
                                    </div>
                                    <span class="progress-text">68% مكتمل</span>
                                </div>
                            </div>
                            
                            <div class="reading-content">
                                <p>"في عالم يتسارع فيه الزمن، تبقى الكتب هي النافذة الوحيدة التي تأخذنا إلى عوالم لا محدودة من المعرفة والخيال..."</p>
                            </div>
                            
                            <div class="interface-controls">
                                <button class="control-btn" data-tooltip="إضافة إشارة مرجعية">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                    </svg>
                                </button>
                                <button class="control-btn" data-tooltip="تمييز النص">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4"/>
                                    </svg>
                                </button>
                                <button class="control-btn" data-tooltip="مشاركة">
                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="floating-ui-elements">
                    <div class="ui-element notification" data-animate="fadeInUp" data-delay="1400">
                        <div class="notification-icon">📚</div>
                        <div class="notification-content">
                            <h5>كتاب جديد متاح!</h5>
                            <p>تم إضافة "العادات الذرية" إلى مكتبتك</p>
                        </div>
                    </div>
                    
                    <div class="ui-element stats-card" data-animate="fadeInUp" data-delay="1600">
                        <h5>إحصائيات القراءة</h5>
                        <div class="stats-grid">
                            <div class="stat">
                                <span class="stat-value">12</span>
                                <span class="stat-label">كتاب هذا الشهر</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">45</span>
                                <span class="stat-label">ساعة قراءة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="scroll-indicator" data-animate="fadeInUp" data-delay="1800">
            <div class="scroll-arrow">
                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                </svg>
            </div>
            <span>اكتشف المزيد</span>
        </div>
    </section>

    <!-- Advanced Features Section -->
    <section id="features" class="features-advanced" data-parallax-layer="2">
        <div class="container">
            <div class="section-header" data-animate="fadeInUp">
                <h2 class="section-title">تقنيات متطورة لتجربة قراءة استثنائية</h2>
                <p class="section-subtitle">اكتشف مميزات BookVerse التي تجعلها الخيار الأول للقراء في العالم العربي</p>
            </div>

            <div class="features-showcase">
                <!-- AI Reading Assistant -->
                <div class="feature-card ai-feature" data-animate="scaleInBounce" data-delay="200">
                    <div class="feature-icon">
                        <div class="ai-brain">
                            <div class="brain-core"></div>
                            <div class="neural-network">
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                            </div>
                        </div>
                    </div>
                    <div class="feature-content">
                        <h3>مساعد القراءة الذكي</h3>
                        <p>ذكاء اصطناعي متطور يتعلم من عادات قراءتك ويقترح الكتب المناسبة لك بدقة 95%</p>
                        <ul class="feature-list">
                            <li>توصيات شخصية ذكية</li>
                            <li>تحليل أسلوب القراءة</li>
                            <li>اقتراحات بناءً على المزاج</li>
                        </ul>
                        <button class="btn btn-primary" data-magnetic="0.3">جرب الآن</button>
                    </div>
                </div>

                <!-- 3D Virtual Library -->
                <div class="feature-card vr-feature" data-animate="fadeInLeft" data-delay="400">
                    <div class="feature-icon">
                        <div class="vr-headset">
                            <div class="headset-body"></div>
                            <div class="headset-lens left"></div>
                            <div class="headset-lens right"></div>
                        </div>
                    </div>
                    <div class="feature-content">
                        <h3>مكتبة ثلاثية الأبعاد</h3>
                        <p>تجول في مكتبة افتراضية مذهلة واستكشف الكتب بطريقة تفاعلية لم تشهدها من قبل</p>
                        <ul class="feature-list">
                            <li>تجربة غامرة بتقنية WebGL</li>
                            <li>تفاعل طبيعي مع الكتب</li>
                            <li>بيئات قراءة متنوعة</li>
                        </ul>
                        <button class="btn btn-secondary" data-magnetic="0.3" onclick="window.BookViewer3D?.toggle3DViewer()">
                            <span>استكشف المكتبة</span>
                            <small>(اضغط Ctrl+3)</small>
                        </button>
                    </div>
                </div>

                <!-- Smart Reading Analytics -->
                <div class="feature-card analytics-feature" data-animate="fadeInRight" data-delay="600">
                    <div class="feature-icon">
                        <div class="analytics-chart">
                            <div class="chart-bar" style="height: 60%"></div>
                            <div class="chart-bar" style="height: 80%"></div>
                            <div class="chart-bar" style="height: 45%"></div>
                            <div class="chart-bar" style="height: 90%"></div>
                            <div class="chart-bar" style="height: 70%"></div>
                        </div>
                    </div>
                    <div class="feature-content">
                        <h3>تحليلات القراءة الذكية</h3>
                        <p>احصل على رؤى عميقة حول عادات قراءتك وتقدمك مع تقارير تفصيلية وإحصائيات متقدمة</p>
                        <ul class="feature-list">
                            <li>تتبع سرعة القراءة</li>
                            <li>تحليل أوقات النشاط</li>
                            <li>إحصائيات شاملة</li>
                        </ul>
                        <button class="btn btn-primary" data-magnetic="0.3">عرض التحليلات</button>
                    </div>
                </div>
            </div>

            <!-- Interactive Demo -->
            <div class="interactive-demo" data-animate="fadeInUp" data-delay="800">
                <div class="demo-container">
                    <div class="demo-screen">
                        <div class="demo-header">
                            <div class="demo-controls">
                                <span class="control red"></span>
                                <span class="control yellow"></span>
                                <span class="control green"></span>
                            </div>
                            <h4>BookVerse Reader</h4>
                        </div>
                        <div class="demo-content">
                            <div class="demo-sidebar">
                                <div class="sidebar-item active">📚 مكتبتي</div>
                                <div class="sidebar-item">⭐ المفضلة</div>
                                <div class="sidebar-item">📊 الإحصائيات</div>
                                <div class="sidebar-item">🎯 التوصيات</div>
                            </div>
                            <div class="demo-main">
                                <div class="demo-book-grid">
                                    <div class="demo-book" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #4ecdc4, #44a08d);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #45b7d1, #96c93d);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #f39c12, #f1c40f);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #9b59b6, #8e44ad);"></div>
                                    <div class="demo-book" style="background: linear-gradient(45deg, #e74c3c, #c0392b);"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="demo-features">
                        <div class="demo-feature">
                            <div class="feature-icon-small">🚀</div>
                            <span>تحميل فوري</span>
                        </div>
                        <div class="demo-feature">
                            <div class="feature-icon-small">🔄</div>
                            <span>مزامنة تلقائية</span>
                        </div>
                        <div class="demo-feature">
                            <div class="feature-icon-small">📱</div>
                            <span>متوافق مع جميع الأجهزة</span>
                        </div>
                        <div class="demo-feature">
                            <div class="feature-icon-small">🔒</div>
                            <span>حماية متقدمة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Stats -->
    <section class="performance-stats">
        <div class="container">
            <div class="stats-grid" data-animate="fadeInUp">
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-number" data-count="99">0</div>
                    <div class="stat-label">نقاط الأداء</div>
                    <div class="stat-description">سرعة تحميل استثنائية</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🔒</div>
                    <div class="stat-number" data-count="100">0</div>
                    <div class="stat-label">% أمان</div>
                    <div class="stat-description">حماية بيانات متقدمة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🌍</div>
                    <div class="stat-number" data-count="150">0</div>
                    <div class="stat-label">دولة</div>
                    <div class="stat-description">انتشار عالمي</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-number" data-count="98">0</div>
                    <div class="stat-label">% رضا المستخدمين</div>
                    <div class="stat-description">تقييمات ممتازة</div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript Files -->
    <script src="js/performance-optimizer.js"></script>
    <script src="js/webgl-effects.js"></script>
    <script src="js/advanced-animations.js"></script>
    <script src="js/3d-book-viewer.js"></script>
    <script src="js/ai-recommendations.js"></script>
    <script src="js/main.js"></script></script>
    
    <!-- Analytics -->
    <script>
        // Performance monitoring
        window.addEventListener('load', () => {
            // Hide loading screen
            const loadingScreen = document.getElementById('loadingScreen');
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
            }, 1500);
            
            // Track page load time
            const loadTime = performance.now();
            console.log(`🚀 Page loaded in ${Math.round(loadTime)}ms`);
        });
    </script>
</body>
</html>
