/**
 * AI-Powered Recommendation Engine
 * Advanced machine learning for personalized book recommendations
 */

class AIRecommendationEngine {
    constructor() {
        this.userProfile = this.loadUserProfile();
        this.bookDatabase = new Map();
        this.userInteractions = [];
        this.modelWeights = {
            genre: 0.3,
            author: 0.2,
            rating: 0.25,
            readingTime: 0.15,
            similarity: 0.1
        };
        this.isInitialized = false;
        this.neuralNetwork = null;
    }

    async init() {
        try {
            await this.loadBookDatabase();
            await this.initializeNeuralNetwork();
            this.setupUserTracking();
            this.startRecommendationEngine();
            
            this.isInitialized = true;
            console.log('🤖 AI Recommendation Engine initialized');
            
        } catch (error) {
            console.warn('AI Recommendation Engine failed to initialize:', error);
            this.fallbackToRuleBasedRecommendations();
        }
    }

    async loadBookDatabase() {
        // Simulate loading comprehensive book database
        const books = [
            {
                id: 1,
                title: 'الأسود يليق بك',
                author: 'أحلام مستغانمي',
                genre: ['رومانسية', 'أدب عربي'],
                rating: 4.8,
                readingTime: 320,
                tags: ['حب', 'ذكريات', 'جزائر', 'عاطفة'],
                description: 'رواية عاطفية تحكي قصة حب معقدة',
                publishYear: 1993,
                pageCount: 350,
                language: 'ar',
                features: ['bestseller', 'award_winning']
            },
            {
                id: 2,
                title: 'مئة عام من العزلة',
                author: 'غابرييل غارسيا ماركيز',
                genre: ['خيال', 'أدب عالمي'],
                rating: 4.9,
                readingTime: 420,
                tags: ['سحرية', 'عائلة', 'تاريخ', 'أمريكا اللاتينية'],
                description: 'رواية ملحمية تتبع عائلة بوينديا',
                publishYear: 1967,
                pageCount: 448,
                language: 'ar',
                features: ['classic', 'nobel_prize']
            },
            {
                id: 3,
                title: 'العادات الذرية',
                author: 'جيمس كلير',
                genre: ['تطوير ذات', 'علم نفس'],
                rating: 4.7,
                readingTime: 280,
                tags: ['عادات', 'إنتاجية', 'تحفيز', 'نجاح'],
                description: 'دليل عملي لبناء عادات جيدة',
                publishYear: 2018,
                pageCount: 320,
                language: 'ar',
                features: ['practical', 'bestseller']
            },
            {
                id: 4,
                title: 'فكر تصبح غنياً',
                author: 'نابليون هيل',
                genre: ['تطوير ذات', 'أعمال'],
                rating: 4.6,
                readingTime: 350,
                tags: ['ثراء', 'نجاح', 'تفكير', 'أهداف'],
                description: 'كتاب كلاسيكي في تطوير الذات',
                publishYear: 1937,
                pageCount: 400,
                language: 'ar',
                features: ['classic', 'business']
            },
            {
                id: 5,
                title: 'الخيميائي',
                author: 'باولو كويلو',
                genre: ['فلسفة', 'روحانية'],
                rating: 4.5,
                readingTime: 200,
                tags: ['رحلة', 'أحلام', 'حكمة', 'روحانية'],
                description: 'رواية فلسفية عن البحث عن الأحلام',
                publishYear: 1988,
                pageCount: 163,
                language: 'ar',
                features: ['philosophical', 'inspirational']
            }
        ];

        books.forEach(book => {
            this.bookDatabase.set(book.id, book);
        });

        console.log(`📚 Loaded ${books.length} books into database`);
    }

    async initializeNeuralNetwork() {
        // Simplified neural network for recommendations
        this.neuralNetwork = {
            inputLayer: 10,  // User features
            hiddenLayer: 20, // Hidden neurons
            outputLayer: 5,  // Book categories
            weights: {
                inputToHidden: this.generateRandomWeights(10, 20),
                hiddenToOutput: this.generateRandomWeights(20, 5)
            },
            biases: {
                hidden: new Array(20).fill(0).map(() => Math.random() - 0.5),
                output: new Array(5).fill(0).map(() => Math.random() - 0.5)
            }
        };

        console.log('🧠 Neural network initialized');
    }

    generateRandomWeights(inputSize, outputSize) {
        const weights = [];
        for (let i = 0; i < inputSize; i++) {
            weights[i] = [];
            for (let j = 0; j < outputSize; j++) {
                weights[i][j] = Math.random() * 2 - 1; // Random between -1 and 1
            }
        }
        return weights;
    }

    setupUserTracking() {
        // Track user interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-book-id]')) {
                const bookId = parseInt(e.target.closest('[data-book-id]').dataset.bookId);
                this.trackInteraction('click', bookId);
            }
        });

        // Track reading time
        let startTime = Date.now();
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                const readingTime = Date.now() - startTime;
                this.trackInteraction('reading_time', null, { duration: readingTime });
            } else {
                startTime = Date.now();
            }
        });

        // Track scroll behavior
        let scrollDepth = 0;
        window.addEventListener('scroll', this.throttle(() => {
            const currentDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (currentDepth > scrollDepth) {
                scrollDepth = currentDepth;
                this.trackInteraction('scroll', null, { depth: scrollDepth });
            }
        }, 1000));
    }

    trackInteraction(type, bookId = null, metadata = {}) {
        const interaction = {
            type,
            bookId,
            timestamp: Date.now(),
            metadata,
            sessionId: this.getSessionId()
        };

        this.userInteractions.push(interaction);
        this.updateUserProfile(interaction);

        // Limit interaction history
        if (this.userInteractions.length > 1000) {
            this.userInteractions = this.userInteractions.slice(-500);
        }

        console.log('📊 Tracked interaction:', interaction);
    }

    updateUserProfile(interaction) {
        if (interaction.bookId) {
            const book = this.bookDatabase.get(interaction.bookId);
            if (book) {
                // Update genre preferences
                book.genre.forEach(genre => {
                    this.userProfile.preferences.genres[genre] = 
                        (this.userProfile.preferences.genres[genre] || 0) + 1;
                });

                // Update author preferences
                this.userProfile.preferences.authors[book.author] = 
                    (this.userProfile.preferences.authors[book.author] || 0) + 1;

                // Update reading patterns
                this.userProfile.readingPatterns.averageRating = 
                    (this.userProfile.readingPatterns.averageRating + book.rating) / 2;
                
                this.userProfile.readingPatterns.preferredLength = 
                    (this.userProfile.readingPatterns.preferredLength + book.readingTime) / 2;
            }
        }

        // Update activity metrics
        this.userProfile.activity.totalInteractions++;
        this.userProfile.activity.lastActive = Date.now();

        // Save profile
        this.saveUserProfile();
    }

    async generateRecommendations(count = 5) {
        if (!this.isInitialized) {
            return this.getFallbackRecommendations(count);
        }

        try {
            // Get user feature vector
            const userVector = this.getUserFeatureVector();
            
            // Calculate recommendations using neural network
            const predictions = this.forwardPass(userVector);
            
            // Score all books
            const scoredBooks = Array.from(this.bookDatabase.values()).map(book => ({
                book,
                score: this.calculateBookScore(book, predictions, userVector)
            }));

            // Sort by score and filter
            const recommendations = scoredBooks
                .filter(item => !this.userProfile.readBooks.includes(item.book.id))
                .sort((a, b) => b.score - a.score)
                .slice(0, count)
                .map(item => ({
                    ...item.book,
                    recommendationScore: item.score,
                    reason: this.generateRecommendationReason(item.book, item.score)
                }));

            console.log('🎯 Generated AI recommendations:', recommendations);
            return recommendations;

        } catch (error) {
            console.error('Error generating recommendations:', error);
            return this.getFallbackRecommendations(count);
        }
    }

    getUserFeatureVector() {
        const profile = this.userProfile;
        
        // Create feature vector based on user profile
        const features = [
            // Genre preferences (normalized)
            this.normalizeGenrePreferences(),
            // Reading activity level
            Math.min(profile.activity.totalInteractions / 100, 1),
            // Average rating preference
            profile.readingPatterns.averageRating / 5,
            // Preferred reading length (normalized)
            Math.min(profile.readingPatterns.preferredLength / 500, 1),
            // Recency of activity
            this.getActivityRecency(),
            // Diversity preference
            this.calculateDiversityPreference(),
            // Time of day preference
            this.getTimePreference(),
            // Device preference
            this.getDevicePreference(),
            // Social influence
            this.getSocialInfluence(),
            // Seasonal preference
            this.getSeasonalPreference()
        ];

        return features;
    }

    normalizeGenrePreferences() {
        const genres = this.userProfile.preferences.genres;
        const total = Object.values(genres).reduce((sum, count) => sum + count, 0);
        return total > 0 ? Math.max(...Object.values(genres)) / total : 0.5;
    }

    getActivityRecency() {
        const lastActive = this.userProfile.activity.lastActive;
        const daysSinceActive = (Date.now() - lastActive) / (1000 * 60 * 60 * 24);
        return Math.max(0, 1 - daysSinceActive / 30); // Decay over 30 days
    }

    calculateDiversityPreference() {
        const genres = Object.keys(this.userProfile.preferences.genres);
        return Math.min(genres.length / 10, 1); // Normalize to 0-1
    }

    getTimePreference() {
        const hour = new Date().getHours();
        // Morning readers might prefer different content than night readers
        return hour < 12 ? 0.3 : hour < 18 ? 0.7 : 0.5;
    }

    getDevicePreference() {
        // Mobile users might prefer shorter content
        return window.innerWidth < 768 ? 0.3 : 0.7;
    }

    getSocialInfluence() {
        // Placeholder for social features
        return 0.5;
    }

    getSeasonalPreference() {
        const month = new Date().getMonth();
        // Different seasons might influence reading preferences
        return (month % 12) / 12;
    }

    forwardPass(inputVector) {
        // Forward propagation through neural network
        const hiddenLayer = this.activateLayer(
            inputVector,
            this.neuralNetwork.weights.inputToHidden,
            this.neuralNetwork.biases.hidden
        );

        const outputLayer = this.activateLayer(
            hiddenLayer,
            this.neuralNetwork.weights.hiddenToOutput,
            this.neuralNetwork.biases.output
        );

        return outputLayer;
    }

    activateLayer(inputs, weights, biases) {
        const outputs = [];
        for (let i = 0; i < weights[0].length; i++) {
            let sum = biases[i];
            for (let j = 0; j < inputs.length; j++) {
                sum += inputs[j] * weights[j][i];
            }
            outputs[i] = this.sigmoid(sum);
        }
        return outputs;
    }

    sigmoid(x) {
        return 1 / (1 + Math.exp(-x));
    }

    calculateBookScore(book, predictions, userVector) {
        let score = 0;

        // Genre matching
        const genreScore = this.calculateGenreScore(book);
        score += genreScore * this.modelWeights.genre;

        // Author preference
        const authorScore = this.calculateAuthorScore(book);
        score += authorScore * this.modelWeights.author;

        // Rating influence
        const ratingScore = book.rating / 5;
        score += ratingScore * this.modelWeights.rating;

        // Reading time preference
        const timeScore = this.calculateReadingTimeScore(book);
        score += timeScore * this.modelWeights.readingTime;

        // Neural network prediction
        const nnScore = Math.max(...predictions);
        score += nnScore * this.modelWeights.similarity;

        // Apply randomness for diversity
        score += (Math.random() - 0.5) * 0.1;

        return Math.max(0, Math.min(1, score));
    }

    calculateGenreScore(book) {
        const userGenres = this.userProfile.preferences.genres;
        const totalInteractions = Object.values(userGenres).reduce((sum, count) => sum + count, 0);
        
        if (totalInteractions === 0) return 0.5;

        let score = 0;
        book.genre.forEach(genre => {
            score += (userGenres[genre] || 0) / totalInteractions;
        });

        return Math.min(score, 1);
    }

    calculateAuthorScore(book) {
        const userAuthors = this.userProfile.preferences.authors;
        const totalInteractions = Object.values(userAuthors).reduce((sum, count) => sum + count, 0);
        
        if (totalInteractions === 0) return 0.5;

        return Math.min((userAuthors[book.author] || 0) / totalInteractions, 1);
    }

    calculateReadingTimeScore(book) {
        const preferredTime = this.userProfile.readingPatterns.preferredLength;
        if (preferredTime === 0) return 0.5;

        const difference = Math.abs(book.readingTime - preferredTime);
        return Math.max(0, 1 - difference / preferredTime);
    }

    generateRecommendationReason(book, score) {
        const reasons = [];

        if (this.userProfile.preferences.genres[book.genre[0]] > 0) {
            reasons.push(`لأنك تحب ${book.genre[0]}`);
        }

        if (this.userProfile.preferences.authors[book.author] > 0) {
            reasons.push(`لأنك قرأت للمؤلف من قبل`);
        }

        if (book.rating > 4.5) {
            reasons.push('تقييم عالي من القراء');
        }

        if (book.features.includes('bestseller')) {
            reasons.push('من الكتب الأكثر مبيعاً');
        }

        return reasons.length > 0 ? reasons[0] : 'مقترح خصيصاً لك';
    }

    startRecommendationEngine() {
        // Generate recommendations periodically
        setInterval(() => {
            this.generateRecommendations().then(recommendations => {
                this.displayRecommendations(recommendations);
            });
        }, 30000); // Every 30 seconds

        // Initial recommendations
        setTimeout(() => {
            this.generateRecommendations().then(recommendations => {
                this.displayRecommendations(recommendations);
            });
        }, 5000);
    }

    displayRecommendations(recommendations) {
        // Create or update recommendations widget
        let widget = document.getElementById('ai-recommendations');
        
        if (!widget) {
            widget = document.createElement('div');
            widget.id = 'ai-recommendations';
            widget.className = 'ai-recommendations-widget';
            document.body.appendChild(widget);
        }

        widget.innerHTML = `
            <div class="recommendations-header">
                <h3>🤖 مقترحات ذكية لك</h3>
                <button class="close-recommendations">&times;</button>
            </div>
            <div class="recommendations-list">
                ${recommendations.map(book => `
                    <div class="recommendation-item" data-book-id="${book.id}">
                        <div class="book-cover-mini" style="background: linear-gradient(45deg, #${Math.floor(Math.random()*16777215).toString(16)}, #${Math.floor(Math.random()*16777215).toString(16)});">
                            <span class="book-title-mini">${book.title}</span>
                        </div>
                        <div class="recommendation-info">
                            <h4>${book.title}</h4>
                            <p>${book.author}</p>
                            <span class="recommendation-reason">${book.reason}</span>
                            <div class="recommendation-score">
                                <span>توافق: ${Math.round(book.recommendationScore * 100)}%</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Add event listeners
        widget.querySelector('.close-recommendations').addEventListener('click', () => {
            widget.style.display = 'none';
        });

        widget.querySelectorAll('.recommendation-item').forEach(item => {
            item.addEventListener('click', () => {
                const bookId = parseInt(item.dataset.bookId);
                this.trackInteraction('recommendation_click', bookId);
                // Handle book selection
            });
        });

        // Show widget
        widget.style.display = 'block';
    }

    getFallbackRecommendations(count) {
        // Simple rule-based recommendations as fallback
        const books = Array.from(this.bookDatabase.values());
        return books
            .sort((a, b) => b.rating - a.rating)
            .slice(0, count)
            .map(book => ({
                ...book,
                recommendationScore: book.rating / 5,
                reason: 'كتاب مميز'
            }));
    }

    fallbackToRuleBasedRecommendations() {
        console.log('📋 Using rule-based recommendations as fallback');
        this.isInitialized = true; // Allow basic functionality
    }

    loadUserProfile() {
        const saved = localStorage.getItem('userProfile');
        if (saved) {
            return JSON.parse(saved);
        }

        return {
            preferences: {
                genres: {},
                authors: {},
                tags: {}
            },
            readingPatterns: {
                averageRating: 0,
                preferredLength: 0,
                readingSpeed: 0
            },
            activity: {
                totalInteractions: 0,
                lastActive: Date.now(),
                sessionsCount: 0
            },
            readBooks: [],
            favoriteBooks: [],
            createdAt: Date.now()
        };
    }

    saveUserProfile() {
        localStorage.setItem('userProfile', JSON.stringify(this.userProfile));
    }

    getSessionId() {
        let sessionId = sessionStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Public API methods
    async getPersonalizedRecommendations(count = 5) {
        return await this.generateRecommendations(count);
    }

    markBookAsRead(bookId) {
        if (!this.userProfile.readBooks.includes(bookId)) {
            this.userProfile.readBooks.push(bookId);
            this.saveUserProfile();
        }
    }

    addToFavorites(bookId) {
        if (!this.userProfile.favoriteBooks.includes(bookId)) {
            this.userProfile.favoriteBooks.push(bookId);
            this.trackInteraction('favorite', bookId);
            this.saveUserProfile();
        }
    }

    rateBook(bookId, rating) {
        this.trackInteraction('rating', bookId, { rating });
        // Update user's rating patterns
        this.userProfile.readingPatterns.averageRating = 
            (this.userProfile.readingPatterns.averageRating + rating) / 2;
        this.saveUserProfile();
    }

    destroy() {
        // Cleanup
        this.saveUserProfile();
        this.isInitialized = false;
    }
}

// Add CSS for recommendations widget
const recommendationStyles = document.createElement('style');
recommendationStyles.textContent = `
    .ai-recommendations-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 350px;
        max-height: 500px;
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        display: none;
        overflow: hidden;
        border: 1px solid #e5e7eb;
    }

    .recommendations-header {
        padding: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .recommendations-header h3 {
        margin: 0;
        font-size: 1rem;
    }

    .close-recommendations {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .recommendations-list {
        max-height: 400px;
        overflow-y: auto;
        padding: 1rem;
    }

    .recommendation-item {
        display: flex;
        gap: 1rem;
        padding: 0.75rem;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: background-color 0.2s ease;
        margin-bottom: 0.5rem;
    }

    .recommendation-item:hover {
        background: #f3f4f6;
    }

    .book-cover-mini {
        width: 40px;
        height: 60px;
        border-radius: 4px;
        display: flex;
        align-items: flex-end;
        padding: 0.25rem;
        flex-shrink: 0;
    }

    .book-title-mini {
        font-size: 0.6rem;
        color: white;
        font-weight: bold;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        line-height: 1;
    }

    .recommendation-info {
        flex: 1;
        min-width: 0;
    }

    .recommendation-info h4 {
        margin: 0 0 0.25rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.2;
    }

    .recommendation-info p {
        margin: 0 0 0.25rem 0;
        font-size: 0.75rem;
        color: #6b7280;
    }

    .recommendation-reason {
        font-size: 0.7rem;
        color: #059669;
        font-weight: 500;
        display: block;
        margin-bottom: 0.25rem;
    }

    .recommendation-score {
        font-size: 0.7rem;
        color: #7c3aed;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .ai-recommendations-widget {
            width: calc(100% - 40px);
            right: 20px;
            left: 20px;
        }
    }
`;

document.head.appendChild(recommendationStyles);

// Initialize AI Recommendation Engine
const aiRecommendations = new AIRecommendationEngine();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => aiRecommendations.init(), 3000);
    });
} else {
    setTimeout(() => aiRecommendations.init(), 3000);
}

// Export for global access
window.AIRecommendations = aiRecommendations;
