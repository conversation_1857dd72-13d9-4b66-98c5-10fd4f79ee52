/**
 * Premium WebGL Effects System
 * Advanced 3D Graphics and Particle Systems
 */

class WebGLEffectsEngine {
    constructor() {
        this.canvas = null;
        this.gl = null;
        this.programs = {};
        this.buffers = {};
        this.textures = {};
        this.particles = [];
        this.animationId = null;
        this.time = 0;
        this.mouse = { x: 0, y: 0 };
        this.isInitialized = false;
    }

    init() {
        try {
            this.createCanvas();
            this.initWebGL();
            this.createShaders();
            this.setupParticleSystem();
            this.bindEvents();
            this.startRenderLoop();
            this.isInitialized = true;
            console.log('🎨 WebGL Effects Engine initialized successfully');
        } catch (error) {
            console.warn('WebGL not supported, falling back to CSS animations:', error);
            this.fallbackToCSSAnimations();
        }
    }

    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.canvas.id = 'webgl-canvas';
        this.canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0.8;
        `;
        document.body.appendChild(this.canvas);
        this.resizeCanvas();
    }

    initWebGL() {
        this.gl = this.canvas.getContext('webgl') || this.canvas.getContext('experimental-webgl');
        if (!this.gl) {
            throw new Error('WebGL not supported');
        }

        this.gl.enable(this.gl.BLEND);
        this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
        this.gl.clearColor(0.0, 0.0, 0.0, 0.0);
    }

    createShaders() {
        // Vertex Shader
        const vertexShaderSource = `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            attribute float a_alpha;
            attribute float a_size;
            
            uniform vec2 u_resolution;
            uniform float u_time;
            
            varying vec2 v_texCoord;
            varying float v_alpha;
            
            void main() {
                vec2 position = a_position;
                
                // Add wave effect
                position.x += sin(u_time * 0.001 + a_position.y * 0.01) * 10.0;
                position.y += cos(u_time * 0.0015 + a_position.x * 0.01) * 5.0;
                
                vec2 clipSpace = ((position / u_resolution) * 2.0) - 1.0;
                gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);
                gl_PointSize = a_size;
                
                v_texCoord = a_texCoord;
                v_alpha = a_alpha;
            }
        `;

        // Fragment Shader
        const fragmentShaderSource = `
            precision mediump float;
            
            uniform float u_time;
            uniform vec2 u_mouse;
            
            varying vec2 v_texCoord;
            varying float v_alpha;
            
            void main() {
                vec2 center = vec2(0.5, 0.5);
                float dist = distance(gl_PointCoord, center);
                
                if (dist > 0.5) {
                    discard;
                }
                
                // Create gradient effect
                float alpha = (1.0 - dist * 2.0) * v_alpha;
                
                // Color based on time and position
                vec3 color = vec3(
                    0.5 + 0.5 * sin(u_time * 0.001),
                    0.5 + 0.5 * sin(u_time * 0.0015 + 2.0),
                    0.5 + 0.5 * sin(u_time * 0.002 + 4.0)
                );
                
                gl_FragColor = vec4(color, alpha);
            }
        `;

        this.programs.particles = this.createProgram(vertexShaderSource, fragmentShaderSource);
    }

    createProgram(vertexSource, fragmentSource) {
        const vertexShader = this.createShader(this.gl.VERTEX_SHADER, vertexSource);
        const fragmentShader = this.createShader(this.gl.FRAGMENT_SHADER, fragmentSource);
        
        const program = this.gl.createProgram();
        this.gl.attachShader(program, vertexShader);
        this.gl.attachShader(program, fragmentShader);
        this.gl.linkProgram(program);
        
        if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
            throw new Error('Program linking failed: ' + this.gl.getProgramInfoLog(program));
        }
        
        return program;
    }

    createShader(type, source) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);
        
        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            throw new Error('Shader compilation failed: ' + this.gl.getShaderInfoLog(shader));
        }
        
        return shader;
    }

    setupParticleSystem() {
        const particleCount = 100;
        this.particles = [];
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 20 + 5,
                alpha: Math.random() * 0.5 + 0.1,
                life: Math.random() * 1000 + 500
            });
        }
        
        this.createParticleBuffers();
    }

    createParticleBuffers() {
        const positions = [];
        const alphas = [];
        const sizes = [];
        
        this.particles.forEach(particle => {
            positions.push(particle.x, particle.y);
            alphas.push(particle.alpha);
            sizes.push(particle.size);
        });
        
        // Position buffer
        this.buffers.position = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.position);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(positions), this.gl.DYNAMIC_DRAW);
        
        // Alpha buffer
        this.buffers.alpha = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.alpha);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(alphas), this.gl.DYNAMIC_DRAW);
        
        // Size buffer
        this.buffers.size = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.size);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(sizes), this.gl.DYNAMIC_DRAW);
    }

    updateParticles() {
        this.particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life--;
            
            // Wrap around screen
            if (particle.x < 0) particle.x = this.canvas.width;
            if (particle.x > this.canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = this.canvas.height;
            if (particle.y > this.canvas.height) particle.y = 0;
            
            // Respawn particle
            if (particle.life <= 0) {
                particle.x = Math.random() * this.canvas.width;
                particle.y = Math.random() * this.canvas.height;
                particle.life = Math.random() * 1000 + 500;
            }
            
            // Mouse interaction
            const dx = particle.x - this.mouse.x;
            const dy = particle.y - this.mouse.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 100) {
                const force = (100 - distance) / 100;
                particle.vx += (dx / distance) * force * 0.1;
                particle.vy += (dy / distance) * force * 0.1;
            }
            
            // Apply friction
            particle.vx *= 0.99;
            particle.vy *= 0.99;
        });
        
        this.updateParticleBuffers();
    }

    updateParticleBuffers() {
        const positions = [];
        const alphas = [];
        const sizes = [];
        
        this.particles.forEach(particle => {
            positions.push(particle.x, particle.y);
            alphas.push(particle.alpha);
            sizes.push(particle.size);
        });
        
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.position);
        this.gl.bufferSubData(this.gl.ARRAY_BUFFER, 0, new Float32Array(positions));
        
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.alpha);
        this.gl.bufferSubData(this.gl.ARRAY_BUFFER, 0, new Float32Array(alphas));
        
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.size);
        this.gl.bufferSubData(this.gl.ARRAY_BUFFER, 0, new Float32Array(sizes));
    }

    render() {
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);
        
        this.gl.useProgram(this.programs.particles);
        
        // Set uniforms
        const resolutionLocation = this.gl.getUniformLocation(this.programs.particles, 'u_resolution');
        this.gl.uniform2f(resolutionLocation, this.canvas.width, this.canvas.height);
        
        const timeLocation = this.gl.getUniformLocation(this.programs.particles, 'u_time');
        this.gl.uniform1f(timeLocation, this.time);
        
        const mouseLocation = this.gl.getUniformLocation(this.programs.particles, 'u_mouse');
        this.gl.uniform2f(mouseLocation, this.mouse.x, this.mouse.y);
        
        // Set attributes
        const positionLocation = this.gl.getAttribLocation(this.programs.particles, 'a_position');
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.position);
        this.gl.enableVertexAttribArray(positionLocation);
        this.gl.vertexAttribPointer(positionLocation, 2, this.gl.FLOAT, false, 0, 0);
        
        const alphaLocation = this.gl.getAttribLocation(this.programs.particles, 'a_alpha');
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.alpha);
        this.gl.enableVertexAttribArray(alphaLocation);
        this.gl.vertexAttribPointer(alphaLocation, 1, this.gl.FLOAT, false, 0, 0);
        
        const sizeLocation = this.gl.getAttribLocation(this.programs.particles, 'a_size');
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.size);
        this.gl.enableVertexAttribArray(sizeLocation);
        this.gl.vertexAttribPointer(sizeLocation, 1, this.gl.FLOAT, false, 0, 0);
        
        // Draw particles
        this.gl.drawArrays(this.gl.POINTS, 0, this.particles.length);
    }

    startRenderLoop() {
        const animate = () => {
            this.time = performance.now();
            this.updateParticles();
            this.render();
            this.animationId = requestAnimationFrame(animate);
        };
        animate();
    }

    bindEvents() {
        window.addEventListener('resize', () => this.resizeCanvas());
        
        document.addEventListener('mousemove', (e) => {
            this.mouse.x = e.clientX;
            this.mouse.y = e.clientY;
        });
        
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
    }

    resizeCanvas() {
        if (!this.canvas) return;
        
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        if (this.gl) {
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        }
    }

    pause() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    resume() {
        if (!this.animationId && this.isInitialized) {
            this.startRenderLoop();
        }
    }

    destroy() {
        this.pause();
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
        this.isInitialized = false;
    }

    fallbackToCSSAnimations() {
        // Create CSS-based particle system as fallback
        const particleContainer = document.createElement('div');
        particleContainer.id = 'css-particles';
        particleContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        `;
        
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 10 + 5}px;
                height: ${Math.random() * 10 + 5}px;
                background: radial-gradient(circle, rgba(99,102,241,0.6) 0%, transparent 70%);
                border-radius: 50%;
                animation: float-particle ${Math.random() * 20 + 10}s linear infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                opacity: ${Math.random() * 0.5 + 0.1};
            `;
            particleContainer.appendChild(particle);
        }
        
        document.body.appendChild(particleContainer);
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float-particle {
                0% {
                    transform: translateY(0px) translateX(0px) rotate(0deg);
                }
                33% {
                    transform: translateY(-100px) translateX(50px) rotate(120deg);
                }
                66% {
                    transform: translateY(-50px) translateX(-50px) rotate(240deg);
                }
                100% {
                    transform: translateY(-200px) translateX(0px) rotate(360deg);
                }
            }
        `;
        document.head.appendChild(style);
        
        console.log('🎨 CSS Particle System initialized as WebGL fallback');
    }
}

// Initialize WebGL Effects
const webglEffects = new WebGLEffectsEngine();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => webglEffects.init());
} else {
    webglEffects.init();
}

// Export for global access
window.WebGLEffects = webglEffects;
