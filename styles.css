/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --success-color: #10b981;

    /* Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --text-white: #ffffff;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-dark: #111827;
    --bg-darker: #030712;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-full: 9999px;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Typography */
    --font-primary: 'Tajawal', sans-serif;
    --font-secondary: 'Noto Kufi Arabic', sans-serif;
    --font-display: 'Amiri', serif;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-light: #9ca3af;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    transition: background-color var(--transition), color var(--transition);
}

body.no-scroll {
    overflow: hidden;
}

/* Custom Cursor */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: difference;
}

.cursor-follower {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: transform 0.3s ease;
    opacity: 0.5;
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    color: white;
}

.book-loader {
    width: 60px;
    height: 40px;
    position: relative;
    margin: 0 auto 2rem;
}

.book-page {
    position: absolute;
    width: 20px;
    height: 40px;
    background: white;
    border-radius: 2px;
    animation: flip 1.5s infinite;
}

.book-page:nth-child(1) {
    left: 0;
    animation-delay: 0s;
}

.book-page:nth-child(2) {
    left: 20px;
    animation-delay: 0.5s;
}

.book-page:nth-child(3) {
    left: 40px;
    animation-delay: 1s;
}

@keyframes flip {
    0%, 50% {
        transform: rotateY(0deg);
    }
    100% {
        transform: rotateY(180deg);
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--space-md);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-title {
    font-size: clamp(2rem, 5vw, 3rem);
    color: var(--text-primary);
    position: relative;
    margin-bottom: var(--space-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    font-family: var(--font-primary);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.btn-large {
    padding: var(--space-md) var(--space-xl);
    font-size: 1rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-icon:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.bounce {
    animation: bounce 0.3s ease;
}

@keyframes bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: var(--z-fixed);
    transition: all var(--transition);
    border-bottom: 1px solid transparent;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .header {
    background: rgba(17, 24, 39, 0.95);
}

[data-theme="dark"] .header.scrolled {
    background: rgba(17, 24, 39, 0.98);
}

.navbar {
    padding: var(--space-md) 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
}

.logo-icon {
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: var(--font-display);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-xl);
    margin: 0;
    padding: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.875rem;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-full);
    transition: all var(--transition);
    position: relative;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--primary-color);
    background: var(--bg-secondary);
}

.nav-link:hover::before {
    width: 80%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.search-btn,
.cart-btn,
.theme-toggle {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.search-btn:hover,
.cart-btn:hover,
.theme-toggle:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
}

.login-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius);
    transition: all var(--transition);
}

.nav-toggle:hover {
    background: var(--bg-secondary);
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: all var(--transition);
    border-radius: 2px;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition);
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.search-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    max-width: 600px;
    width: 90%;
    position: relative;
    box-shadow: var(--shadow-xl);
}

.search-box {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-xl);
}

.search-input {
    flex: 1;
    padding: var(--space-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1.125rem;
    outline: none;
    transition: all var(--transition);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-submit {
    padding: var(--space-md) var(--space-lg);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition);
}

.search-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.search-close {
    position: absolute;
    top: var(--space-lg);
    right: var(--space-lg);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition);
}

.search-close:hover {
    background: var(--danger-color);
    color: white;
}

.search-suggestions h4 {
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-sm);
}

.suggestion {
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition);
}

.suggestion:hover {
    background: var(--primary-color);
    color: white;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: var(--gradient-primary);
    overflow: hidden;
    padding-top: 80px;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="particles" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23particles)"/></svg>');
    opacity: 0.3;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.floating-books {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-book {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float-random var(--duration, 10s) ease-in-out infinite;
    animation-delay: var(--delay, 0s);
}

.floating-book:nth-child(1) { top: 20%; left: 10%; }
.floating-book:nth-child(2) { top: 60%; left: 80%; }
.floating-book:nth-child(3) { top: 30%; left: 70%; }
.floating-book:nth-child(4) { top: 80%; left: 20%; }
.floating-book:nth-child(5) { top: 10%; left: 50%; }

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    color: white;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    margin-bottom: var(--space-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    margin-bottom: var(--space-lg);
    line-height: 1.1;
}

.title-line {
    display: block;
    opacity: 0.9;
}

.title-highlight {
    display: block;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
}

.hero-subtitle {
    font-size: 1.125rem;
    margin-bottom: var(--space-xl);
    opacity: 0.9;
    line-height: 1.6;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: #ffd700;
    font-family: var(--font-secondary);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
    flex-wrap: wrap;
}

.hero-features {
    display: flex;
    gap: var(--space-xl);
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 0.875rem;
    opacity: 0.9;
}

.feature-item i {
    color: #ffd700;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-device {
    position: relative;
    width: 300px;
    height: 500px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 30px;
    padding: 20px;
    box-shadow: var(--shadow-xl);
    border: 3px solid rgba(255, 255, 255, 0.1);
}

.device-screen {
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.reading-interface {
    padding: var(--space-lg);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.book-cover {
    width: 120px;
    height: 160px;
    margin: 0 auto var(--space-lg);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.book-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reading-progress {
    margin-bottom: var(--space-lg);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.reading-text {
    flex: 1;
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-primary);
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.element {
    position: absolute;
    font-size: 1.5rem;
    opacity: 0.6;
    animation: float-element 4s ease-in-out infinite;
}

.element-1 {
    top: 10%;
    right: -20px;
    animation-delay: 0s;
}

.element-2 {
    top: 30%;
    left: -20px;
    animation-delay: 1s;
}

.element-3 {
    bottom: 30%;
    right: -30px;
    animation-delay: 2s;
}

.element-4 {
    bottom: 10%;
    left: -30px;
    animation-delay: 3s;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: var(--space-xl);
    left: 50%;
    transform: translateX(-50%);
    color: white;
    opacity: 0.7;
    animation: bounce-vertical 2s ease-in-out infinite;
}

.scroll-arrow {
    font-size: 1.5rem;
}

@keyframes float-random {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(5deg);
    }
    50% {
        transform: translateY(-10px) rotate(-5deg);
    }
    75% {
        transform: translateY(-30px) rotate(3deg);
    }
}

@keyframes float-element {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes bounce-vertical {
    0%, 100% {
        transform: translateX(-50%) translateY(0px);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
    }
}

/* Features Section */
.features {
    padding: 5rem 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
}

/* Books Section */
.books {
    padding: 5rem 0;
}

.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.book-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.book-card:hover {
    transform: translateY(-5px);
}

.book-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.book-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.book-card:hover .book-image img {
    transform: scale(1.1);
}

.book-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.book-card:hover .book-overlay {
    opacity: 1;
}

.book-info {
    padding: 1.5rem;
}

.book-info h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.book-author {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.book-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.book-rating i {
    color: var(--accent-color);
}

.book-rating span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.book-price {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.old-price {
    text-decoration: line-through;
    color: var(--text-secondary);
}

.text-center {
    text-align: center;
}

/* Newsletter Section */
.newsletter {
    padding: 4rem 0;
    background: var(--gradient);
    color: white;
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter h2 {
    margin-bottom: 1rem;
}

.newsletter p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
}

.newsletter-form button {
    white-space: nowrap;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: white;
}

.footer-section p {
    opacity: 0.8;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.8;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .hero-buttons {
        justify-content: center;
    }
}
