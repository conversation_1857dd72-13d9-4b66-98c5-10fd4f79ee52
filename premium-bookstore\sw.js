/**
 * Service Worker for Premium BookStore
 * Advanced caching and offline functionality
 */

const CACHE_NAME = 'bookverse-premium-v1.0.0';
const STATIC_CACHE = 'bookverse-static-v1.0.0';
const DYNAMIC_CACHE = 'bookverse-dynamic-v1.0.0';
const IMAGE_CACHE = 'bookverse-images-v1.0.0';

// Resources to cache immediately
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/css/core.css',
    '/css/grid.css',
    '/css/components.css',
    '/js/performance-optimizer.js',
    '/js/webgl-effects.js',
    '/js/advanced-animations.js',
    '/js/3d-book-viewer.js',
    '/js/ai-recommendations.js',
    '/js/main.js',
    '/favicon.svg',
    '/favicon.png'
];

// Resources to cache on demand
const DYNAMIC_ASSETS = [
    '/api/',
    '/images/',
    '/fonts/'
];

// Maximum cache sizes
const MAX_CACHE_SIZE = {
    static: 50,
    dynamic: 100,
    images: 200
};

// Cache strategies
const CACHE_STRATEGIES = {
    static: 'cache-first',
    dynamic: 'network-first',
    images: 'cache-first',
    api: 'network-first'
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
    console.log('📦 Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                console.log('📥 Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            }),
            self.skipWaiting()
        ])
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== IMAGE_CACHE) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            self.clients.claim()
        ])
    );
});

// Fetch event - handle requests with appropriate strategy
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    try {
        // Determine cache strategy based on request type
        if (isStaticAsset(pathname)) {
            return await cacheFirstStrategy(request, STATIC_CACHE);
        } else if (isImageRequest(request)) {
            return await cacheFirstStrategy(request, IMAGE_CACHE);
        } else if (isAPIRequest(pathname)) {
            return await networkFirstStrategy(request, DYNAMIC_CACHE);
        } else if (isDynamicAsset(pathname)) {
            return await networkFirstStrategy(request, DYNAMIC_CACHE);
        } else {
            // Default to network first for unknown requests
            return await networkFirstStrategy(request, DYNAMIC_CACHE);
        }
    } catch (error) {
        console.error('❌ Fetch error:', error);
        return await handleOfflineRequest(request);
    }
}

// Cache-first strategy (for static assets)
async function cacheFirstStrategy(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
        // Update cache in background
        updateCacheInBackground(request, cache);
        return cachedResponse;
    }
    
    // Not in cache, fetch from network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
        // Clone response before caching
        const responseClone = networkResponse.clone();
        await cache.put(request, responseClone);
        await limitCacheSize(cacheName, MAX_CACHE_SIZE.static);
    }
    
    return networkResponse;
}

// Network-first strategy (for dynamic content)
async function networkFirstStrategy(request, cacheName) {
    const cache = await caches.open(cacheName);
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Clone response before caching
            const responseClone = networkResponse.clone();
            await cache.put(request, responseClone);
            await limitCacheSize(cacheName, MAX_CACHE_SIZE.dynamic);
        }
        
        return networkResponse;
    } catch (error) {
        // Network failed, try cache
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

// Update cache in background
async function updateCacheInBackground(request, cache) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            await cache.put(request, networkResponse.clone());
        }
    } catch (error) {
        // Silently fail background updates
        console.warn('⚠️ Background cache update failed:', error);
    }
}

// Handle offline requests
async function handleOfflineRequest(request) {
    const url = new URL(request.url);
    
    // Try to find cached version
    const cacheNames = [STATIC_CACHE, DYNAMIC_CACHE, IMAGE_CACHE];
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
    }
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
        return await createOfflinePage();
    }
    
    // Return offline image for image requests
    if (isImageRequest(request)) {
        return await createOfflineImage();
    }
    
    // Return generic offline response
    return new Response('Offline', {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'text/plain' }
    });
}

// Create offline page
async function createOfflinePage() {
    const offlineHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>غير متصل - BookVerse</title>
            <style>
                body {
                    font-family: 'Tajawal', sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                }
                .offline-container {
                    max-width: 500px;
                    padding: 2rem;
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                }
                h1 {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }
                p {
                    font-size: 1.1rem;
                    margin-bottom: 2rem;
                    opacity: 0.9;
                }
                .retry-btn {
                    background: rgba(255, 255, 255, 0.2);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 1rem 2rem;
                    border-radius: 2rem;
                    font-size: 1rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                .retry-btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: translateY(-2px);
                }
            </style>
        </head>
        <body>
            <div class="offline-container">
                <div class="offline-icon">📚</div>
                <h1>غير متصل بالإنترنت</h1>
                <p>يبدو أنك غير متصل بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.</p>
                <button class="retry-btn" onclick="window.location.reload()">
                    إعادة المحاولة
                </button>
            </div>
        </body>
        </html>
    `;
    
    return new Response(offlineHTML, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
}

// Create offline image placeholder
async function createOfflineImage() {
    // Create a simple SVG placeholder
    const svg = `
        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
            <rect width="400" height="300" fill="#f3f4f6"/>
            <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="16" fill="#6b7280">
                📚 غير متاح حالياً
            </text>
        </svg>
    `;
    
    return new Response(svg, {
        headers: { 'Content-Type': 'image/svg+xml' }
    });
}

// Utility functions
function isStaticAsset(pathname) {
    return STATIC_ASSETS.some(asset => pathname.includes(asset)) ||
           pathname.endsWith('.css') ||
           pathname.endsWith('.js') ||
           pathname.endsWith('.html') ||
           pathname === '/';
}

function isImageRequest(request) {
    return request.destination === 'image' ||
           request.url.includes('/images/') ||
           /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i.test(request.url);
}

function isAPIRequest(pathname) {
    return pathname.startsWith('/api/');
}

function isDynamicAsset(pathname) {
    return DYNAMIC_ASSETS.some(asset => pathname.startsWith(asset));
}

// Limit cache size
async function limitCacheSize(cacheName, maxSize) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    
    if (keys.length > maxSize) {
        // Remove oldest entries
        const keysToDelete = keys.slice(0, keys.length - maxSize);
        await Promise.all(keysToDelete.map(key => cache.delete(key)));
        console.log(`🧹 Cleaned ${keysToDelete.length} items from ${cacheName}`);
    }
}

// Background sync for analytics
self.addEventListener('sync', (event) => {
    if (event.tag === 'analytics-sync') {
        event.waitUntil(syncAnalytics());
    }
});

async function syncAnalytics() {
    try {
        // Get stored analytics data
        const analyticsData = await getStoredAnalytics();
        
        if (analyticsData.length > 0) {
            // Send to analytics endpoint
            const response = await fetch('/api/analytics/batch', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analyticsData)
            });
            
            if (response.ok) {
                // Clear stored data
                await clearStoredAnalytics();
                console.log('📊 Analytics synced successfully');
            }
        }
    } catch (error) {
        console.error('❌ Analytics sync failed:', error);
    }
}

// Push notifications
self.addEventListener('push', (event) => {
    if (!event.data) return;
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/favicon.png',
        badge: '/favicon.png',
        data: data.data,
        actions: data.actions || [],
        requireInteraction: data.requireInteraction || false
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    const data = event.notification.data;
    
    event.waitUntil(
        clients.openWindow(data.url || '/')
    );
});

// Message handling from main thread
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CACHE_ANALYTICS':
            storeAnalyticsData(data);
            break;
            
        case 'GET_CACHE_STATUS':
            getCacheStatus().then(status => {
                event.ports[0].postMessage(status);
            });
            break;
            
        default:
            console.log('Unknown message type:', type);
    }
});

// Helper functions for analytics storage
async function getStoredAnalytics() {
    // Implementation would use IndexedDB or similar
    return [];
}

async function clearStoredAnalytics() {
    // Implementation would clear IndexedDB
}

async function storeAnalyticsData(data) {
    // Implementation would store in IndexedDB
}

async function getCacheStatus() {
    const cacheNames = await caches.keys();
    const status = {};
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        status[cacheName] = keys.length;
    }
    
    return status;
}

console.log('🔧 Service Worker loaded successfully');
