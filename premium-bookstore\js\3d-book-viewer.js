/**
 * 3D Book Viewer with WebGL
 * Advanced 3D book preview and virtual library
 */

class ThreeDBookViewer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.books = [];
        this.controls = null;
        this.raycaster = null;
        this.mouse = new THREE.Vector2();
        this.isInitialized = false;
        this.currentBook = null;
        this.animationId = null;
    }

    async init() {
        try {
            // Check if Three.js is available
            if (typeof THREE === 'undefined') {
                await this.loadThreeJS();
            }
            
            this.setupScene();
            this.setupCamera();
            this.setupRenderer();
            this.setupLights();
            this.setupControls();
            this.setupRaycaster();
            this.createVirtualLibrary();
            this.bindEvents();
            this.startRenderLoop();
            
            this.isInitialized = true;
            console.log('📚 3D Book Viewer initialized');
            
        } catch (error) {
            console.warn('3D Book Viewer failed to initialize:', error);
            this.fallbackTo2D();
        }
    }

    async loadThreeJS() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);
        this.scene.fog = new THREE.Fog(0xf0f0f0, 1, 5000);
    }

    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 5, 10);
    }

    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1;
        
        // Create container
        const container = document.createElement('div');
        container.id = 'book-viewer-3d';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.5s ease;
        `;
        
        container.appendChild(this.renderer.domElement);
        document.body.appendChild(container);
    }

    setupLights() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // Point lights for ambiance
        const pointLight1 = new THREE.PointLight(0xff6b6b, 0.5, 50);
        pointLight1.position.set(-10, 5, 0);
        this.scene.add(pointLight1);
        
        const pointLight2 = new THREE.PointLight(0x4ecdc4, 0.5, 50);
        pointLight2.position.set(10, 5, 0);
        this.scene.add(pointLight2);
    }

    setupControls() {
        // Simple orbit controls simulation
        this.controls = {
            enabled: false,
            autoRotate: true,
            autoRotateSpeed: 0.5,
            target: new THREE.Vector3(0, 0, 0)
        };
    }

    setupRaycaster() {
        this.raycaster = new THREE.Raycaster();
    }

    createVirtualLibrary() {
        // Create bookshelf
        this.createBookshelf();
        
        // Create floating books
        this.createFloatingBooks();
        
        // Create environment
        this.createEnvironment();
    }

    createBookshelf() {
        const shelfGeometry = new THREE.BoxGeometry(12, 0.2, 2);
        const shelfMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        
        for (let i = 0; i < 5; i++) {
            const shelf = new THREE.Mesh(shelfGeometry, shelfMaterial);
            shelf.position.y = i * 2 - 4;
            shelf.position.z = -5;
            shelf.castShadow = true;
            shelf.receiveShadow = true;
            this.scene.add(shelf);
        }
    }

    createFloatingBooks() {
        const bookData = [
            { title: 'الأسود يليق بك', color: 0xff6b6b, position: [-3, 2, 0] },
            { title: 'مئة عام من العزلة', color: 0x4ecdc4, position: [0, 3, 1] },
            { title: 'العادات الذرية', color: 0x45b7d1, position: [3, 1, -1] },
            { title: 'فكر تصبح غنياً', color: 0xf39c12, position: [-2, 0, 2] },
            { title: 'الخيميائي', color: 0x9b59b6, position: [2, 4, 0] }
        ];

        bookData.forEach((data, index) => {
            const book = this.createBook(data.title, data.color);
            book.position.set(...data.position);
            book.userData = { title: data.title, index };
            
            // Add floating animation
            book.userData.originalY = data.position[1];
            book.userData.floatSpeed = 0.02 + Math.random() * 0.01;
            book.userData.floatRange = 0.5 + Math.random() * 0.3;
            
            this.books.push(book);
            this.scene.add(book);
        });
    }

    createBook(title, color) {
        const group = new THREE.Group();
        
        // Book cover
        const coverGeometry = new THREE.BoxGeometry(1.5, 2, 0.1);
        const coverMaterial = new THREE.MeshPhongMaterial({ 
            color: color,
            shininess: 30
        });
        const cover = new THREE.Mesh(coverGeometry, coverMaterial);
        cover.castShadow = true;
        cover.receiveShadow = true;
        group.add(cover);
        
        // Book pages
        const pagesGeometry = new THREE.BoxGeometry(1.4, 1.9, 0.3);
        const pagesMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
        const pages = new THREE.Mesh(pagesGeometry, pagesMaterial);
        pages.position.z = -0.2;
        pages.castShadow = true;
        pages.receiveShadow = true;
        group.add(pages);
        
        // Book spine
        const spineGeometry = new THREE.BoxGeometry(0.1, 2, 0.3);
        const spineMaterial = new THREE.MeshPhongMaterial({ 
            color: new THREE.Color(color).multiplyScalar(0.8)
        });
        const spine = new THREE.Mesh(spineGeometry, spineMaterial);
        spine.position.x = -0.75;
        spine.position.z = -0.15;
        spine.castShadow = true;
        spine.receiveShadow = true;
        group.add(spine);
        
        // Add title text (simplified)
        this.addBookTitle(group, title);
        
        return group;
    }

    addBookTitle(bookGroup, title) {
        // Create a simple text representation using planes
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 128;
        
        context.fillStyle = 'rgba(255, 255, 255, 0.9)';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#333';
        context.font = 'bold 20px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        
        // Wrap text
        const words = title.split(' ');
        const lines = [];
        let currentLine = '';
        
        words.forEach(word => {
            const testLine = currentLine + word + ' ';
            const metrics = context.measureText(testLine);
            if (metrics.width > 200 && currentLine !== '') {
                lines.push(currentLine);
                currentLine = word + ' ';
            } else {
                currentLine = testLine;
            }
        });
        lines.push(currentLine);
        
        lines.forEach((line, index) => {
            context.fillText(line, canvas.width / 2, (canvas.height / 2) + (index - lines.length / 2 + 0.5) * 25);
        });
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.MeshBasicMaterial({ 
            map: texture,
            transparent: true
        });
        const geometry = new THREE.PlaneGeometry(1.2, 0.6);
        const textMesh = new THREE.Mesh(geometry, material);
        textMesh.position.z = 0.06;
        bookGroup.add(textMesh);
    }

    createEnvironment() {
        // Floor
        const floorGeometry = new THREE.PlaneGeometry(50, 50);
        const floorMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = -5;
        floor.receiveShadow = true;
        this.scene.add(floor);
        
        // Particles
        this.createParticles();
    }

    createParticles() {
        const particleCount = 100;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 50;
            positions[i + 1] = Math.random() * 20;
            positions[i + 2] = (Math.random() - 0.5) * 50;
        }
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            color: 0x888888,
            size: 0.1,
            transparent: true,
            opacity: 0.6
        });
        
        const particleSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(particleSystem);
    }

    bindEvents() {
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('mousemove', (e) => this.onMouseMove(e));
        window.addEventListener('click', (e) => this.onMouseClick(e));
        
        // Show/hide 3D viewer
        document.addEventListener('keydown', (e) => {
            if (e.key === '3' && e.ctrlKey) {
                e.preventDefault();
                this.toggle3DViewer();
            }
        });
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;
        
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    onMouseMove(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // Update camera position based on mouse
        if (this.camera) {
            this.camera.position.x = this.mouse.x * 2;
            this.camera.position.y = 5 + this.mouse.y * 2;
            this.camera.lookAt(this.controls.target);
        }
    }

    onMouseClick(event) {
        if (!this.isInitialized) return;
        
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.books, true);
        
        if (intersects.length > 0) {
            const clickedBook = intersects[0].object.parent;
            this.selectBook(clickedBook);
        }
    }

    selectBook(book) {
        // Reset previous selection
        if (this.currentBook) {
            this.currentBook.scale.set(1, 1, 1);
        }
        
        // Highlight selected book
        this.currentBook = book;
        book.scale.set(1.2, 1.2, 1.2);
        
        // Show book details
        this.showBookDetails(book.userData.title);
        
        console.log('📖 Selected book:', book.userData.title);
    }

    showBookDetails(title) {
        const detailsOverlay = document.createElement('div');
        detailsOverlay.className = 'book-details-3d';
        detailsOverlay.innerHTML = `
            <div class="book-details-content">
                <h3>${title}</h3>
                <p>عرض ثلاثي الأبعاد للكتاب</p>
                <div class="book-actions">
                    <button class="btn btn-primary">قراءة الآن</button>
                    <button class="btn btn-secondary">إضافة للسلة</button>
                </div>
                <button class="close-details">&times;</button>
            </div>
        `;
        
        document.body.appendChild(detailsOverlay);
        
        // Close details
        const closeBtn = detailsOverlay.querySelector('.close-details');
        closeBtn.addEventListener('click', () => {
            detailsOverlay.remove();
        });
        
        setTimeout(() => {
            detailsOverlay.remove();
        }, 5000);
    }

    toggle3DViewer() {
        const container = document.getElementById('book-viewer-3d');
        if (!container) return;
        
        const isVisible = container.style.opacity === '1';
        
        if (isVisible) {
            container.style.opacity = '0';
            container.style.pointerEvents = 'none';
        } else {
            container.style.opacity = '1';
            container.style.pointerEvents = 'auto';
        }
    }

    startRenderLoop() {
        const animate = () => {
            if (!this.isInitialized) return;
            
            this.animationId = requestAnimationFrame(animate);
            this.updateAnimations();
            this.render();
        };
        
        animate();
    }

    updateAnimations() {
        const time = Date.now() * 0.001;
        
        // Animate floating books
        this.books.forEach(book => {
            if (book.userData.floatSpeed) {
                book.position.y = book.userData.originalY + 
                    Math.sin(time * book.userData.floatSpeed) * book.userData.floatRange;
                book.rotation.y += 0.005;
            }
        });
        
        // Auto-rotate camera
        if (this.controls.autoRotate) {
            this.camera.position.x = Math.cos(time * this.controls.autoRotateSpeed) * 10;
            this.camera.position.z = Math.sin(time * this.controls.autoRotateSpeed) * 10;
            this.camera.lookAt(this.controls.target);
        }
    }

    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    fallbackTo2D() {
        console.log('📱 Falling back to 2D book viewer');
        
        // Create 2D book viewer
        const viewer2D = document.createElement('div');
        viewer2D.id = 'book-viewer-2d';
        viewer2D.innerHTML = `
            <div class="book-grid-2d">
                <div class="book-item-2d" data-title="الأسود يليق بك">
                    <div class="book-cover-2d" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);">
                        <h4>الأسود يليق بك</h4>
                    </div>
                </div>
                <div class="book-item-2d" data-title="مئة عام من العزلة">
                    <div class="book-cover-2d" style="background: linear-gradient(45deg, #4ecdc4, #44a08d);">
                        <h4>مئة عام من العزلة</h4>
                    </div>
                </div>
                <div class="book-item-2d" data-title="العادات الذرية">
                    <div class="book-cover-2d" style="background: linear-gradient(45deg, #45b7d1, #96c93d);">
                        <h4>العادات الذرية</h4>
                    </div>
                </div>
            </div>
        `;
        
        // Add styles
        const styles = document.createElement('style');
        styles.textContent = `
            #book-viewer-2d {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 1000;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s ease;
            }
            
            .book-grid-2d {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 2rem;
                padding: 2rem;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 1rem;
                backdrop-filter: blur(10px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            }
            
            .book-item-2d {
                cursor: pointer;
                transition: transform 0.3s ease;
            }
            
            .book-item-2d:hover {
                transform: translateY(-10px) scale(1.05);
            }
            
            .book-cover-2d {
                width: 120px;
                height: 160px;
                border-radius: 8px;
                display: flex;
                align-items: flex-end;
                padding: 1rem;
                color: white;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            }
            
            .book-cover-2d h4 {
                font-size: 0.9rem;
                line-height: 1.2;
            }
        `;
        
        document.head.appendChild(styles);
        document.body.appendChild(viewer2D);
        
        // Add click handlers
        viewer2D.querySelectorAll('.book-item-2d').forEach(item => {
            item.addEventListener('click', () => {
                const title = item.dataset.title;
                this.showBookDetails(title);
            });
        });
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        const container = document.getElementById('book-viewer-3d');
        if (container) {
            container.remove();
        }
        
        this.isInitialized = false;
    }
}

// Initialize 3D Book Viewer
const bookViewer3D = new ThreeDBookViewer();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => bookViewer3D.init(), 2000); // Delay to ensure other systems are loaded
    });
} else {
    setTimeout(() => bookViewer3D.init(), 2000);
}

// Export for global access
window.BookViewer3D = bookViewer3D;
