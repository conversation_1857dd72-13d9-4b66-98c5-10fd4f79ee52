/**
 * Main Application Controller
 * Premium BookStore - World-Class Implementation
 */

class PremiumBookStore {
    constructor() {
        this.isInitialized = false;
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.cart = JSON.parse(localStorage.getItem('cart')) || [];
        this.user = JSON.parse(localStorage.getItem('user')) || null;
        this.searchIndex = new Map();
        this.observers = new Map();
        this.eventListeners = new Map();
    }

    async init() {
        try {
            await this.initializeCore();
            await this.initializeUI();
            await this.initializeInteractions();
            await this.initializeAnalytics();
            
            this.isInitialized = true;
            console.log('🚀 Premium BookStore initialized successfully');
            
            // Dispatch custom event
            window.dispatchEvent(new CustomEvent('bookstore:ready', {
                detail: { timestamp: Date.now() }
            }));
            
        } catch (error) {
            console.error('❌ Failed to initialize BookStore:', error);
            this.handleInitializationError(error);
        }
    }

    async initializeCore() {
        // Set initial theme
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        
        // Initialize search index
        await this.buildSearchIndex();
        
        // Setup service worker
        await this.registerServiceWorker();
        
        // Initialize cart
        this.updateCartUI();
        
        console.log('✅ Core systems initialized');
    }

    async initializeUI() {
        // Setup navigation
        this.setupNavigation();
        
        // Setup theme toggle
        this.setupThemeToggle();
        
        // Setup search
        this.setupSearch();
        
        // Setup cart
        this.setupCart();
        
        // Setup scroll effects
        this.setupScrollEffects();
        
        // Setup magnetic effects
        this.setupMagneticEffects();
        
        console.log('✅ UI components initialized');
    }

    async initializeInteractions() {
        // Setup counter animations
        this.setupCounterAnimations();
        
        // Setup demo video
        this.setupDemoVideo();
        
        // Setup form handlers
        this.setupFormHandlers();
        
        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        console.log('✅ Interactions initialized');
    }

    async initializeAnalytics() {
        // Track page view
        this.trackPageView();
        
        // Setup user behavior tracking
        this.setupBehaviorTracking();
        
        // Setup performance monitoring
        this.setupPerformanceMonitoring();
        
        console.log('✅ Analytics initialized');
    }

    setupNavigation() {
        const navbar = document.querySelector('.navbar');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const navLinks = document.querySelectorAll('.nav-link');
        
        // Scroll effect for navbar
        let lastScrollY = window.scrollY;
        
        const handleScroll = () => {
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
            
            // Hide/show navbar on scroll
            if (currentScrollY > lastScrollY && currentScrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollY = currentScrollY;
        };
        
        window.addEventListener('scroll', this.throttle(handleScroll, 16), { passive: true });
        
        // Mobile menu toggle
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => {
                document.body.classList.toggle('mobile-menu-open');
                mobileToggle.classList.toggle('active');
            });
        }
        
        // Smooth scroll for nav links
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - navbar.offsetHeight;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
                
                // Close mobile menu
                document.body.classList.remove('mobile-menu-open');
                if (mobileToggle) {
                    mobileToggle.classList.remove('active');
                }
            });
        });
    }

    setupThemeToggle() {
        const themeToggle = document.querySelector('.theme-toggle');
        
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
        
        // Update theme icon
        this.updateThemeIcon();
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
        this.updateThemeIcon();
        
        // Animate theme transition
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
        
        // Track theme change
        this.trackEvent('theme_changed', { theme: this.currentTheme });
    }

    updateThemeIcon() {
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('svg path');
            if (this.currentTheme === 'dark') {
                icon.setAttribute('d', 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z');
            } else {
                icon.setAttribute('d', 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z');
            }
        }
    }

    setupSearch() {
        const searchBtn = document.querySelector('.search-btn');
        const searchOverlay = document.createElement('div');
        searchOverlay.className = 'search-overlay';
        searchOverlay.innerHTML = `
            <div class="search-container">
                <div class="search-header">
                    <h2>البحث في المكتبة</h2>
                    <button class="search-close">&times;</button>
                </div>
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="ابحث عن كتاب، مؤلف، أو موضوع...">
                    <button class="search-submit">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </button>
                </div>
                <div class="search-results"></div>
                <div class="search-suggestions">
                    <h4>اقتراحات شائعة</h4>
                    <div class="suggestions-list">
                        <span class="suggestion">الذكاء الاصطناعي</span>
                        <span class="suggestion">التطوير الشخصي</span>
                        <span class="suggestion">الأدب العربي</span>
                        <span class="suggestion">علم النفس</span>
                        <span class="suggestion">التاريخ الإسلامي</span>
                        <span class="suggestion">البرمجة</span>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(searchOverlay);
        
        // Search functionality
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                searchOverlay.classList.add('active');
                document.body.classList.add('no-scroll');
                setTimeout(() => {
                    searchOverlay.querySelector('.search-input').focus();
                }, 300);
            });
        }
        
        // Close search
        const searchClose = searchOverlay.querySelector('.search-close');
        searchClose.addEventListener('click', () => {
            this.closeSearch();
        });
        
        // Close on overlay click
        searchOverlay.addEventListener('click', (e) => {
            if (e.target === searchOverlay) {
                this.closeSearch();
            }
        });
        
        // Search input handling
        const searchInput = searchOverlay.querySelector('.search-input');
        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
        
        // Suggestions
        const suggestions = searchOverlay.querySelectorAll('.suggestion');
        suggestions.forEach(suggestion => {
            suggestion.addEventListener('click', () => {
                searchInput.value = suggestion.textContent;
                this.performSearch(suggestion.textContent);
            });
        });
    }

    closeSearch() {
        const searchOverlay = document.querySelector('.search-overlay');
        searchOverlay.classList.remove('active');
        document.body.classList.remove('no-scroll');
    }

    async performSearch(query) {
        if (!query.trim()) return;
        
        const searchResults = document.querySelector('.search-results');
        searchResults.innerHTML = '<div class="search-loading">جاري البحث...</div>';
        
        // Simulate search API call
        setTimeout(() => {
            const mockResults = [
                { title: 'الأسود يليق بك', author: 'أحلام مستغانمي', category: 'الأدب العربي' },
                { title: 'مئة عام من العزلة', author: 'غابرييل غارسيا ماركيز', category: 'الأدب العالمي' },
                { title: 'العادات الذرية', author: 'جيمس كلير', category: 'التطوير الشخصي' }
            ];
            
            const filteredResults = mockResults.filter(book => 
                book.title.includes(query) || 
                book.author.includes(query) || 
                book.category.includes(query)
            );
            
            this.displaySearchResults(filteredResults);
        }, 500);
        
        // Track search
        this.trackEvent('search_performed', { query });
    }

    displaySearchResults(results) {
        const searchResults = document.querySelector('.search-results');
        
        if (results.length === 0) {
            searchResults.innerHTML = '<div class="no-results">لم يتم العثور على نتائج</div>';
            return;
        }
        
        const resultsHTML = results.map(book => `
            <div class="search-result-item">
                <div class="result-info">
                    <h4>${book.title}</h4>
                    <p>${book.author}</p>
                    <span class="result-category">${book.category}</span>
                </div>
                <button class="btn btn-primary btn-sm">عرض</button>
            </div>
        `).join('');
        
        searchResults.innerHTML = `<div class="search-results-list">${resultsHTML}</div>`;
    }

    setupCart() {
        const cartBtn = document.querySelector('.cart-btn');
        
        if (cartBtn) {
            cartBtn.addEventListener('click', () => {
                this.openCart();
            });
        }
    }

    openCart() {
        // Create cart overlay
        const cartOverlay = document.createElement('div');
        cartOverlay.className = 'cart-overlay';
        cartOverlay.innerHTML = `
            <div class="cart-container">
                <div class="cart-header">
                    <h2>سلة التسوق</h2>
                    <button class="cart-close">&times;</button>
                </div>
                <div class="cart-content">
                    ${this.cart.length === 0 ? 
                        '<div class="empty-cart">السلة فارغة</div>' : 
                        this.renderCartItems()
                    }
                </div>
                ${this.cart.length > 0 ? `
                    <div class="cart-footer">
                        <div class="cart-total">
                            <span>المجموع: ${this.calculateTotal()} ريال</span>
                        </div>
                        <button class="btn btn-primary btn-large">إتمام الشراء</button>
                    </div>
                ` : ''}
            </div>
        `;
        
        document.body.appendChild(cartOverlay);
        document.body.classList.add('no-scroll');
        
        // Close cart
        const cartClose = cartOverlay.querySelector('.cart-close');
        cartClose.addEventListener('click', () => {
            this.closeCart();
        });
        
        cartOverlay.addEventListener('click', (e) => {
            if (e.target === cartOverlay) {
                this.closeCart();
            }
        });
    }

    closeCart() {
        const cartOverlay = document.querySelector('.cart-overlay');
        if (cartOverlay) {
            cartOverlay.remove();
            document.body.classList.remove('no-scroll');
        }
    }

    addToCart(item) {
        this.cart.push(item);
        localStorage.setItem('cart', JSON.stringify(this.cart));
        this.updateCartUI();
        this.showNotification(`تم إضافة "${item.title}" إلى السلة`, 'success');
        this.trackEvent('add_to_cart', { item_id: item.id, item_title: item.title });
    }

    updateCartUI() {
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = this.cart.length;
        }
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('[data-count]');
        
        const animateCounter = (counter) => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;
            
            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current).toLocaleString();
            }, 16);
        };
        
        // Intersection Observer for counters
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                    animateCounter(entry.target);
                    entry.target.classList.add('counted');
                }
            });
        }, { threshold: 0.5 });
        
        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
        
        this.observers.set('counters', counterObserver);
    }

    setupDemoVideo() {
        const playDemoBtn = document.getElementById('playDemoBtn');
        
        if (playDemoBtn) {
            playDemoBtn.addEventListener('click', () => {
                this.showDemoVideo();
            });
        }
    }

    showDemoVideo() {
        const videoOverlay = document.createElement('div');
        videoOverlay.className = 'video-overlay';
        videoOverlay.innerHTML = `
            <div class="video-container">
                <button class="video-close">&times;</button>
                <div class="video-content">
                    <h3>عرض توضيحي - BookVerse Premium</h3>
                    <div class="video-placeholder">
                        <div class="play-icon">▶</div>
                        <p>فيديو توضيحي لمميزات المنصة</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(videoOverlay);
        document.body.classList.add('no-scroll');
        
        // Close video
        const videoClose = videoOverlay.querySelector('.video-close');
        videoClose.addEventListener('click', () => {
            videoOverlay.remove();
            document.body.classList.remove('no-scroll');
        });
        
        videoOverlay.addEventListener('click', (e) => {
            if (e.target === videoOverlay) {
                videoOverlay.remove();
                document.body.classList.remove('no-scroll');
            }
        });
        
        this.trackEvent('demo_video_opened');
    }

    setupMagneticEffects() {
        const magneticElements = document.querySelectorAll('[data-magnetic]');
        
        magneticElements.forEach(element => {
            const strength = parseFloat(element.dataset.magnetic) || 0.3;
            
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                const moveX = x * strength;
                const moveY = y * strength;
                
                element.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });
            
            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translate(0, 0)';
            });
        });
    }

    setupScrollEffects() {
        const scrollIndicator = document.querySelector('.scroll-indicator');
        
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', () => {
                window.scrollTo({
                    top: window.innerHeight,
                    behavior: 'smooth'
                });
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.querySelector('.search-btn').click();
            }
            
            // Escape to close overlays
            if (e.key === 'Escape') {
                this.closeAllOverlays();
            }
        });
    }

    closeAllOverlays() {
        const overlays = document.querySelectorAll('.search-overlay, .cart-overlay, .video-overlay');
        overlays.forEach(overlay => {
            if (overlay.classList.contains('active') || overlay) {
                overlay.remove();
            }
        });
        document.body.classList.remove('no-scroll');
    }

    // Utility methods
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.hideNotification(notification);
        });
        
        setTimeout(() => {
            this.hideNotification(notification);
        }, 5000);
    }

    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // Analytics methods
    trackPageView() {
        this.trackEvent('page_view', {
            page: window.location.pathname,
            title: document.title,
            timestamp: Date.now()
        });
    }

    trackEvent(eventName, properties = {}) {
        const event = {
            name: eventName,
            properties: {
                ...properties,
                timestamp: Date.now(),
                user_agent: navigator.userAgent,
                theme: this.currentTheme
            }
        };
        
        console.log('📊 Event tracked:', event);
        
        // Send to analytics service
        // fetch('/api/analytics/events', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(event)
        // });
    }

    async buildSearchIndex() {
        // Build search index for better performance
        const books = [
            { id: 1, title: 'الأسود يليق بك', author: 'أحلام مستغانمي', category: 'الأدب العربي' },
            { id: 2, title: 'مئة عام من العزلة', author: 'غابرييل غارسيا ماركيز', category: 'الأدب العالمي' },
            { id: 3, title: 'العادات الذرية', author: 'جيمس كلير', category: 'التطوير الشخصي' }
        ];
        
        books.forEach(book => {
            const searchTerms = [
                book.title,
                book.author,
                book.category,
                ...book.title.split(' '),
                ...book.author.split(' ')
            ];
            
            searchTerms.forEach(term => {
                if (!this.searchIndex.has(term.toLowerCase())) {
                    this.searchIndex.set(term.toLowerCase(), []);
                }
                this.searchIndex.get(term.toLowerCase()).push(book);
            });
        });
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('✅ Service Worker registered:', registration);
            } catch (error) {
                console.log('❌ Service Worker registration failed:', error);
            }
        }
    }

    setupBehaviorTracking() {
        // Track user interactions
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, a, [data-track]')) {
                this.trackEvent('element_clicked', {
                    element: e.target.tagName.toLowerCase(),
                    text: e.target.textContent?.trim().substring(0, 50),
                    class: e.target.className
                });
            }
        });
        
        // Track scroll depth
        let maxScrollDepth = 0;
        window.addEventListener('scroll', this.throttle(() => {
            const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollDepth > maxScrollDepth) {
                maxScrollDepth = scrollDepth;
                if (maxScrollDepth % 25 === 0) {
                    this.trackEvent('scroll_depth', { depth: maxScrollDepth });
                }
            }
        }, 1000));
    }

    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        if ('PerformanceObserver' in window) {
            // Largest Contentful Paint
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.trackEvent('lcp_measured', { value: lastEntry.startTime });
            }).observe({ entryTypes: ['largest-contentful-paint'] });
            
            // First Input Delay
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                entries.forEach(entry => {
                    this.trackEvent('fid_measured', { value: entry.processingStart - entry.startTime });
                });
            }).observe({ entryTypes: ['first-input'] });
        }
    }

    setupFormHandlers() {
        // Handle all forms
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form')) {
                e.preventDefault();
                this.handleFormSubmission(e.target);
            }
        });
    }

    handleFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        this.trackEvent('form_submitted', {
            form_id: form.id || 'unknown',
            form_class: form.className
        });
        
        // Show success message
        this.showNotification('تم إرسال النموذج بنجاح!', 'success');
    }

    handleInitializationError(error) {
        console.error('Initialization failed:', error);
        
        // Show user-friendly error message
        const errorMessage = document.createElement('div');
        errorMessage.className = 'initialization-error';
        errorMessage.innerHTML = `
            <div class="error-content">
                <h3>حدث خطأ في تحميل الموقع</h3>
                <p>يرجى إعادة تحميل الصفحة أو المحاولة لاحقاً</p>
                <button onclick="window.location.reload()" class="btn btn-primary">إعادة تحميل</button>
            </div>
        `;
        
        document.body.appendChild(errorMessage);
    }

    destroy() {
        // Cleanup
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.eventListeners.clear();
        this.isInitialized = false;
    }
}

// Initialize the application
const bookStore = new PremiumBookStore();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => bookStore.init());
} else {
    bookStore.init();
}

// Export for global access
window.BookStore = bookStore;
