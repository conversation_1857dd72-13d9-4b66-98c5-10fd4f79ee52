/**
 * Advanced Animation System
 * Professional-grade animations with GSAP-like functionality
 */

class AdvancedAnimationEngine {
    constructor() {
        this.animations = new Map();
        this.timeline = [];
        this.observers = new Map();
        this.easingFunctions = this.createEasingFunctions();
        this.isInitialized = false;
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupMorphingAnimations();
        this.setupParallaxEffects();
        this.setupMagneticEffects();
        this.isInitialized = true;
        console.log('🎬 Advanced Animation Engine initialized');
    }

    createEasingFunctions() {
        return {
            linear: t => t,
            easeInQuad: t => t * t,
            easeOutQuad: t => t * (2 - t),
            easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
            easeInCubic: t => t * t * t,
            easeOutCubic: t => (--t) * t * t + 1,
            easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
            easeInQuart: t => t * t * t * t,
            easeOutQuart: t => 1 - (--t) * t * t * t,
            easeInOutQuart: t => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,
            easeInQuint: t => t * t * t * t * t,
            easeOutQuint: t => 1 + (--t) * t * t * t * t,
            easeInOutQuint: t => t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t,
            easeInSine: t => 1 - Math.cos(t * Math.PI / 2),
            easeOutSine: t => Math.sin(t * Math.PI / 2),
            easeInOutSine: t => -(Math.cos(Math.PI * t) - 1) / 2,
            easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),
            easeOutExpo: t => t === 1 ? 1 : 1 - Math.pow(2, -10 * t),
            easeInOutExpo: t => {
                if (t === 0) return 0;
                if (t === 1) return 1;
                if (t < 0.5) return Math.pow(2, 20 * t - 10) / 2;
                return (2 - Math.pow(2, -20 * t + 10)) / 2;
            },
            easeInCirc: t => 1 - Math.sqrt(1 - t * t),
            easeOutCirc: t => Math.sqrt(1 - (--t) * t),
            easeInOutCirc: t => t < 0.5 ? (1 - Math.sqrt(1 - 4 * t * t)) / 2 : (Math.sqrt(1 - (-2 * t + 2) * (-2 * t + 2)) + 1) / 2,
            easeInBack: t => 2.70158 * t * t * t - 1.70158 * t * t,
            easeOutBack: t => 1 + 2.70158 * (--t) * t * t + 1.70158 * t * t,
            easeInOutBack: t => {
                const c1 = 1.70158;
                const c2 = c1 * 1.525;
                return t < 0.5
                    ? (Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
                    : (Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2;
            },
            easeInElastic: t => {
                const c4 = (2 * Math.PI) / 3;
                return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
            },
            easeOutElastic: t => {
                const c4 = (2 * Math.PI) / 3;
                return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
            },
            easeInOutElastic: t => {
                const c5 = (2 * Math.PI) / 4.5;
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5
                    ? -(Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * c5)) / 2
                    : (Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * c5)) / 2 + 1;
            },
            easeInBounce: t => 1 - this.easingFunctions.easeOutBounce(1 - t),
            easeOutBounce: t => {
                const n1 = 7.5625;
                const d1 = 2.75;
                if (t < 1 / d1) {
                    return n1 * t * t;
                } else if (t < 2 / d1) {
                    return n1 * (t -= 1.5 / d1) * t + 0.75;
                } else if (t < 2.5 / d1) {
                    return n1 * (t -= 2.25 / d1) * t + 0.9375;
                } else {
                    return n1 * (t -= 2.625 / d1) * t + 0.984375;
                }
            },
            easeInOutBounce: t => t < 0.5
                ? (1 - this.easingFunctions.easeOutBounce(1 - 2 * t)) / 2
                : (1 + this.easingFunctions.easeOutBounce(2 * t - 1)) / 2
        };
    }

    animate(element, properties, options = {}) {
        const defaults = {
            duration: 1000,
            easing: 'easeOutQuart',
            delay: 0,
            onStart: null,
            onUpdate: null,
            onComplete: null
        };

        const config = { ...defaults, ...options };
        const startTime = performance.now() + config.delay;
        const endTime = startTime + config.duration;
        const startValues = {};
        const targetValues = {};

        // Get initial values
        Object.keys(properties).forEach(prop => {
            if (prop === 'transform') {
                // Handle transform properties
                Object.keys(properties[prop]).forEach(transformProp => {
                    const currentValue = this.getTransformValue(element, transformProp);
                    startValues[`transform.${transformProp}`] = currentValue;
                    targetValues[`transform.${transformProp}`] = properties[prop][transformProp];
                });
            } else {
                const currentValue = this.getCurrentValue(element, prop);
                startValues[prop] = currentValue;
                targetValues[prop] = properties[prop];
            }
        });

        const animationId = `anim_${Date.now()}_${Math.random()}`;
        
        const animate = (currentTime) => {
            if (currentTime < startTime) {
                requestAnimationFrame(animate);
                return;
            }

            if (currentTime >= startTime && config.onStart && !this.animations.get(animationId)?.started) {
                config.onStart();
                if (this.animations.has(animationId)) {
                    this.animations.get(animationId).started = true;
                }
            }

            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / config.duration, 1);
            const easedProgress = this.easingFunctions[config.easing](progress);

            // Apply animated values
            Object.keys(targetValues).forEach(prop => {
                const startValue = startValues[prop];
                const targetValue = targetValues[prop];
                const currentValue = startValue + (targetValue - startValue) * easedProgress;

                if (prop.startsWith('transform.')) {
                    const transformProp = prop.split('.')[1];
                    this.setTransformValue(element, transformProp, currentValue);
                } else {
                    this.setElementProperty(element, prop, currentValue);
                }
            });

            if (config.onUpdate) {
                config.onUpdate(progress, easedProgress);
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.animations.delete(animationId);
                if (config.onComplete) {
                    config.onComplete();
                }
            }
        };

        this.animations.set(animationId, {
            element,
            animate,
            started: false
        });

        requestAnimationFrame(animate);
        return animationId;
    }

    getCurrentValue(element, property) {
        const style = getComputedStyle(element);
        const value = style.getPropertyValue(property);
        
        if (property === 'opacity') {
            return parseFloat(value) || 0;
        }
        
        if (property.includes('color')) {
            return this.parseColor(value);
        }
        
        return parseFloat(value) || 0;
    }

    getTransformValue(element, property) {
        const transform = getComputedStyle(element).transform;
        if (transform === 'none') return property.includes('scale') ? 1 : 0;
        
        // Parse transform matrix
        const matrix = transform.match(/matrix.*\((.+)\)/);
        if (matrix) {
            const values = matrix[1].split(', ').map(parseFloat);
            switch (property) {
                case 'translateX': return values[4] || 0;
                case 'translateY': return values[5] || 0;
                case 'scaleX': return values[0] || 1;
                case 'scaleY': return values[3] || 1;
                case 'rotate': return Math.atan2(values[1], values[0]) * (180 / Math.PI) || 0;
                default: return 0;
            }
        }
        
        return property.includes('scale') ? 1 : 0;
    }

    setTransformValue(element, property, value) {
        const currentTransform = element.style.transform || '';
        const transformProps = this.parseTransform(currentTransform);
        
        transformProps[property] = value;
        
        const transformString = Object.entries(transformProps)
            .map(([prop, val]) => {
                if (prop.includes('translate')) return `${prop}(${val}px)`;
                if (prop.includes('scale')) return `${prop}(${val})`;
                if (prop === 'rotate') return `${prop}(${val}deg)`;
                return `${prop}(${val})`;
            })
            .join(' ');
            
        element.style.transform = transformString;
    }

    parseTransform(transformString) {
        const props = {};
        const regex = /(\w+)\(([^)]+)\)/g;
        let match;
        
        while ((match = regex.exec(transformString)) !== null) {
            const [, prop, value] = match;
            props[prop] = parseFloat(value) || 0;
        }
        
        return props;
    }

    setElementProperty(element, property, value) {
        if (property === 'opacity') {
            element.style.opacity = value;
        } else if (property.includes('color')) {
            element.style[property] = this.formatColor(value);
        } else {
            element.style[property] = `${value}px`;
        }
    }

    parseColor(colorString) {
        // Simple RGB parser - extend as needed
        const rgb = colorString.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgb) {
            return {
                r: parseInt(rgb[1]),
                g: parseInt(rgb[2]),
                b: parseInt(rgb[3])
            };
        }
        return { r: 0, g: 0, b: 0 };
    }

    formatColor(colorObj) {
        return `rgb(${Math.round(colorObj.r)}, ${Math.round(colorObj.g)}, ${Math.round(colorObj.b)})`;
    }

    setupIntersectionObserver() {
        const observerOptions = {
            threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],
            rootMargin: '-10% 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;
                const animationType = element.dataset.animate;
                
                if (entry.isIntersecting && !element.classList.contains('animated')) {
                    this.triggerAnimation(element, animationType);
                    element.classList.add('animated');
                }
            });
        }, observerOptions);

        // Observe all elements with data-animate attribute
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });

        this.observers.set('intersection', observer);
    }

    triggerAnimation(element, type) {
        switch (type) {
            case 'fadeInUp':
                this.animate(element, {
                    opacity: 1,
                    transform: { translateY: 0 }
                }, {
                    duration: 800,
                    easing: 'easeOutQuart'
                });
                break;
                
            case 'fadeInLeft':
                this.animate(element, {
                    opacity: 1,
                    transform: { translateX: 0 }
                }, {
                    duration: 800,
                    easing: 'easeOutQuart'
                });
                break;
                
            case 'scaleIn':
                this.animate(element, {
                    opacity: 1,
                    transform: { scale: 1 }
                }, {
                    duration: 600,
                    easing: 'easeOutBack'
                });
                break;
                
            case 'rotateIn':
                this.animate(element, {
                    opacity: 1,
                    transform: { rotate: 0, scale: 1 }
                }, {
                    duration: 800,
                    easing: 'easeOutElastic'
                });
                break;
                
            default:
                this.animate(element, {
                    opacity: 1
                }, {
                    duration: 600,
                    easing: 'easeOutQuart'
                });
        }
    }

    setupScrollAnimations() {
        let ticking = false;
        
        const updateScrollAnimations = () => {
            const scrollY = window.pageYOffset;
            const windowHeight = window.innerHeight;
            
            // Parallax elements
            document.querySelectorAll('[data-parallax]').forEach(element => {
                const speed = parseFloat(element.dataset.parallax) || 0.5;
                const yPos = -(scrollY * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
            
            // Scroll-triggered animations
            document.querySelectorAll('[data-scroll-animation]').forEach(element => {
                const rect = element.getBoundingClientRect();
                const isVisible = rect.top < windowHeight && rect.bottom > 0;
                
                if (isVisible && !element.classList.contains('scroll-animated')) {
                    const animationType = element.dataset.scrollAnimation;
                    this.triggerAnimation(element, animationType);
                    element.classList.add('scroll-animated');
                }
            });
            
            ticking = false;
        };
        
        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }

    setupMorphingAnimations() {
        // SVG path morphing
        document.querySelectorAll('[data-morph]').forEach(element => {
            const targetPath = element.dataset.morph;
            const originalPath = element.getAttribute('d');
            
            element.addEventListener('mouseenter', () => {
                this.morphPath(element, targetPath, 400);
            });
            
            element.addEventListener('mouseleave', () => {
                this.morphPath(element, originalPath, 400);
            });
        });
    }

    morphPath(element, targetPath, duration) {
        // Simplified path morphing - would need a proper SVG library for complex shapes
        const startPath = element.getAttribute('d');
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = this.easingFunctions.easeInOutQuart(progress);
            
            // Simple interpolation - extend for complex paths
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.setAttribute('d', targetPath);
            }
        };
        
        requestAnimationFrame(animate);
    }

    setupParallaxEffects() {
        // Advanced parallax with multiple layers
        const parallaxElements = document.querySelectorAll('[data-parallax-layer]');
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            
            parallaxElements.forEach(element => {
                const layer = parseInt(element.dataset.parallaxLayer) || 1;
                const speed = 0.1 * layer;
                const yPos = -(scrollTop * speed);
                
                element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            });
        }, { passive: true });
    }

    setupMagneticEffects() {
        // Magnetic hover effects
        document.querySelectorAll('[data-magnetic]').forEach(element => {
            const strength = parseFloat(element.dataset.magnetic) || 0.3;
            
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                const moveX = x * strength;
                const moveY = y * strength;
                
                element.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });
            
            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translate(0, 0)';
            });
        });
    }

    // Timeline animations
    createTimeline() {
        return new AnimationTimeline(this);
    }

    // Utility methods
    stagger(elements, animation, staggerDelay = 100) {
        elements.forEach((element, index) => {
            setTimeout(() => {
                this.animate(element, animation.properties, {
                    ...animation.options,
                    delay: index * staggerDelay
                });
            }, 0);
        });
    }

    destroy() {
        this.animations.clear();
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.isInitialized = false;
    }
}

class AnimationTimeline {
    constructor(engine) {
        this.engine = engine;
        this.animations = [];
        this.totalDuration = 0;
    }

    to(element, properties, options = {}) {
        const startTime = this.totalDuration + (options.delay || 0);
        this.animations.push({
            element,
            properties,
            options: { ...options, delay: startTime }
        });
        this.totalDuration = startTime + (options.duration || 1000);
        return this;
    }

    play() {
        this.animations.forEach(anim => {
            this.engine.animate(anim.element, anim.properties, anim.options);
        });
        return this;
    }
}

// Initialize Advanced Animation Engine
const animationEngine = new AdvancedAnimationEngine();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => animationEngine.init());
} else {
    animationEngine.init();
}

// Export for global access
window.AnimationEngine = animationEngine;
